/**
 * DUAL REPORT CONFIGURATION SYSTEM
 * Shared configuration panel for both Pre-Reporting and Final Reporting
 * Implements specification requirements from FINAL REPORT.txt
 */

class DualReportConfiguration {
    constructor() {
        this.config = this.initializeDefaultConfig();
        this.validationRules = this.initializeValidationRules();
        this.eventListeners = new Map();
        
        console.log('⚙️ Dual Report Configuration initialized');
    }

    /**
     * Initialize default configuration
     */
    initializeDefaultConfig() {
        return {
            generatedBy: '',
            designation: '',
            reportType: 'employee-based', // 'employee-based' or 'item-based'
            outputFormat: 'word', // 'word', 'pdf', 'excel'
            lastUpdated: null,
            isValid: false
        };
    }

    /**
     * Initialize validation rules
     */
    initializeValidationRules() {
        return {
            generatedBy: {
                required: true,
                minLength: 2,
                maxLength: 100,
                pattern: /^[a-zA-Z\s.'-]+$/,
                errorMessage: 'Generated By must contain only letters, spaces, periods, apostrophes, and hyphens'
            },
            designation: {
                required: true,
                minLength: 2,
                maxLength: 100,
                pattern: /^[a-zA-Z\s.'-]+$/,
                errorMessage: 'Designation must contain only letters, spaces, periods, apostrophes, and hyphens'
            },
            reportType: {
                required: true,
                allowedValues: ['employee-based', 'item-based'],
                errorMessage: 'Report type must be either employee-based or item-based'
            },
            outputFormat: {
                required: true,
                allowedValues: ['word', 'pdf', 'excel'],
                errorMessage: 'Output format must be word, pdf, or excel'
            }
        };
    }

    /**
     * Render the dual configuration panel
     * @param {string} containerId - ID of container element
     * @returns {string} HTML for the configuration panel
     */
    renderConfigurationPanel(containerId = 'dual-report-config') {
        const panelHTML = `
            <div class="dual-report-configuration" id="${containerId}">
                <div class="config-header">
                    <h4>📋 DUAL REPORT CONFIGURATION</h4>
                    <p class="config-subtitle">Shared configuration for both Pre-Reporting and Final Reporting</p>
                </div>
                
                <div class="config-form">
                    <div class="config-row">
                        <div class="config-field">
                            <label for="config-generated-by">
                                <i class="fas fa-user"></i> Generated By *
                            </label>
                            <input 
                                type="text" 
                                id="config-generated-by" 
                                class="config-input"
                                placeholder="Enter your full name"
                                value="${this.config.generatedBy}"
                                maxlength="100"
                                required
                            />
                            <div class="field-error" id="generated-by-error"></div>
                        </div>
                        
                        <div class="config-field">
                            <label for="config-designation">
                                <i class="fas fa-id-badge"></i> Designation *
                            </label>
                            <input 
                                type="text" 
                                id="config-designation" 
                                class="config-input"
                                placeholder="Enter your job title/designation"
                                value="${this.config.designation}"
                                maxlength="100"
                                required
                            />
                            <div class="field-error" id="designation-error"></div>
                        </div>
                    </div>
                    
                    <div class="config-row smart-reporter-only" style="display: none;">
                        <div class="config-field">
                            <label for="config-report-type">
                                <i class="fas fa-file-alt"></i> Report Type
                            </label>
                            <select id="config-report-type" class="config-select">
                                <option value="employee-based" ${this.config.reportType === 'employee-based' ? 'selected' : ''}>
                                    Employee-Based Report
                                </option>
                                <option value="item-based" ${this.config.reportType === 'item-based' ? 'selected' : ''}>
                                    Item-Based Report
                                </option>
                            </select>
                        </div>
                        
                        <div class="config-field">
                            <label for="config-output-format">
                                <i class="fas fa-file-export"></i> Output Format
                            </label>
                            <select id="config-output-format" class="config-select">
                                <option value="word" ${this.config.outputFormat === 'word' ? 'selected' : ''}>
                                    Word Document (.docx)
                                </option>
                                <option value="pdf" ${this.config.outputFormat === 'pdf' ? 'selected' : ''}>
                                    PDF Document (.pdf)
                                </option>
                                <option value="excel" ${this.config.outputFormat === 'excel' ? 'selected' : ''}>
                                    Excel Spreadsheet (.xlsx)
                                </option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="config-status">
                        <div class="status-indicator" id="config-status">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Configuration incomplete</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        return panelHTML;
    }

    /**
     * Initialize the configuration panel after rendering
     */
    initializePanel(containerId = 'dual-report-config') {
        const container = document.getElementById(containerId);
        if (!container) {
            console.error('❌ Configuration container not found:', containerId);
            return false;
        }

        // Set up event listeners
        this.setupEventListeners(containerId);
        
        // Initial validation
        this.validateConfiguration();
        
        console.log('✅ Dual Report Configuration panel initialized');
        return true;
    }

    /**
     * Set up event listeners for configuration inputs
     */
    setupEventListeners(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        // Generated By input
        const generatedByInput = container.querySelector('#config-generated-by');
        if (generatedByInput) {
            generatedByInput.addEventListener('input', (e) => {
                this.updateConfig('generatedBy', e.target.value);
                this.validateField('generatedBy', e.target.value);
            });
        }

        // Designation input
        const designationInput = container.querySelector('#config-designation');
        if (designationInput) {
            designationInput.addEventListener('input', (e) => {
                this.updateConfig('designation', e.target.value);
                this.validateField('designation', e.target.value);
            });
        }

        // Report Type select
        const reportTypeSelect = container.querySelector('#config-report-type');
        if (reportTypeSelect) {
            reportTypeSelect.addEventListener('change', (e) => {
                this.updateConfig('reportType', e.target.value);
                this.validateField('reportType', e.target.value);
            });
        }

        // Output Format select
        const outputFormatSelect = container.querySelector('#config-output-format');
        if (outputFormatSelect) {
            outputFormatSelect.addEventListener('change', (e) => {
                this.updateConfig('outputFormat', e.target.value);
                this.validateField('outputFormat', e.target.value);
            });
        }
    }

    /**
     * Update configuration value
     */
    updateConfig(field, value) {
        this.config[field] = value;
        this.config.lastUpdated = new Date().toISOString();
        
        // Validate entire configuration
        this.validateConfiguration();
        
        // Notify listeners
        this.notifyConfigurationChange(field, value);
    }

    /**
     * Validate individual field
     */
    validateField(fieldName, value) {
        const rule = this.validationRules[fieldName];
        if (!rule) return { isValid: true };

        const errors = [];

        // Required check
        if (rule.required && (!value || value.trim() === '')) {
            errors.push(`${fieldName} is required`);
        }

        // Length checks
        if (value && rule.minLength && value.length < rule.minLength) {
            errors.push(`${fieldName} must be at least ${rule.minLength} characters`);
        }

        if (value && rule.maxLength && value.length > rule.maxLength) {
            errors.push(`${fieldName} must not exceed ${rule.maxLength} characters`);
        }

        // Pattern check
        if (value && rule.pattern && !rule.pattern.test(value)) {
            errors.push(rule.errorMessage);
        }

        // Allowed values check
        if (value && rule.allowedValues && !rule.allowedValues.includes(value)) {
            errors.push(rule.errorMessage);
        }

        const isValid = errors.length === 0;
        
        // Update UI
        this.updateFieldUI(fieldName, isValid, errors);
        
        return { isValid, errors };
    }

    /**
     * Validate entire configuration
     */
    validateConfiguration() {
        const results = {};
        let overallValid = true;

        // Validate each field
        Object.keys(this.validationRules).forEach(fieldName => {
            const value = this.config[fieldName];
            const result = this.validateField(fieldName, value);
            results[fieldName] = result;
            
            if (!result.isValid) {
                overallValid = false;
            }
        });

        this.config.isValid = overallValid;
        
        // Update status indicator
        this.updateStatusIndicator(overallValid);
        
        return { isValid: overallValid, fieldResults: results };
    }

    /**
     * Update field UI based on validation
     */
    updateFieldUI(fieldName, isValid, errors) {
        const errorElementId = `${fieldName.replace(/([A-Z])/g, '-$1').toLowerCase()}-error`;
        const errorElement = document.getElementById(errorElementId);
        
        if (errorElement) {
            if (isValid) {
                errorElement.textContent = '';
                errorElement.style.display = 'none';
            } else {
                errorElement.textContent = errors[0] || 'Invalid input';
                errorElement.style.display = 'block';
            }
        }
    }

    /**
     * Update status indicator
     */
    updateStatusIndicator(isValid) {
        const statusElement = document.getElementById('config-status');
        if (!statusElement) return;

        const icon = statusElement.querySelector('i');
        const text = statusElement.querySelector('span');

        if (isValid) {
            statusElement.className = 'status-indicator valid';
            icon.className = 'fas fa-check-circle';
            text.textContent = 'Configuration complete';
        } else {
            statusElement.className = 'status-indicator invalid';
            icon.className = 'fas fa-exclamation-triangle';
            text.textContent = 'Configuration incomplete';
        }
    }

    /**
     * Show/hide Smart Reporter specific fields
     */
    toggleSmartReporterFields(show) {
        const smartReporterFields = document.querySelectorAll('.smart-reporter-only');
        smartReporterFields.forEach(field => {
            field.style.display = show ? 'block' : 'none';
        });
    }

    /**
     * Get current configuration
     */
    getConfiguration() {
        return { ...this.config };
    }

    /**
     * Set configuration
     */
    setConfiguration(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.config.lastUpdated = new Date().toISOString();
        
        // Update UI if panel is rendered
        this.updateUIFromConfig();
        
        // Validate
        this.validateConfiguration();
    }

    /**
     * Update UI from current configuration
     */
    updateUIFromConfig() {
        const generatedByInput = document.getElementById('config-generated-by');
        const designationInput = document.getElementById('config-designation');
        const reportTypeSelect = document.getElementById('config-report-type');
        const outputFormatSelect = document.getElementById('config-output-format');

        if (generatedByInput) generatedByInput.value = this.config.generatedBy;
        if (designationInput) designationInput.value = this.config.designation;
        if (reportTypeSelect) reportTypeSelect.value = this.config.reportType;
        if (outputFormatSelect) outputFormatSelect.value = this.config.outputFormat;
    }

    /**
     * Add configuration change listener
     */
    addConfigurationChangeListener(callback) {
        const listenerId = Date.now().toString();
        this.eventListeners.set(listenerId, callback);
        return listenerId;
    }

    /**
     * Remove configuration change listener
     */
    removeConfigurationChangeListener(listenerId) {
        return this.eventListeners.delete(listenerId);
    }

    /**
     * Notify configuration change listeners
     */
    notifyConfigurationChange(field, value) {
        this.eventListeners.forEach(callback => {
            try {
                callback(field, value, this.config);
            } catch (error) {
                console.error('❌ Configuration change listener error:', error);
            }
        });
    }

    /**
     * Reset configuration to defaults
     */
    resetConfiguration() {
        this.config = this.initializeDefaultConfig();
        this.updateUIFromConfig();
        this.validateConfiguration();
    }

    /**
     * Check if configuration is valid for report generation
     */
    isValidForReportGeneration() {
        return this.config.isValid && 
               this.config.generatedBy.trim() !== '' && 
               this.config.designation.trim() !== '';
    }
}

// Export for use in Final Report Interface
window.DualReportConfiguration = DualReportConfiguration;
