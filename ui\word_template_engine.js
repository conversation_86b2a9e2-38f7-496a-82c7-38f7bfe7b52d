/**
 * Word Document Template Engine
 * Generates Word documents matching the exact formatting from the provided template screenshot
 */

console.log('📄 Loading Word Template Engine...');

class WordTemplateEngine {
    constructor() {
        this.templateStructure = this.initializeTemplateStructure();
        console.log('📄 Word Template Engine initialized');
    }

    /**
     * Initialize the exact template structure from the screenshot
     */
    initializeTemplateStructure() {
        return {
            header: {
                title: "PAYROLL AUDIT REPORT: {MONTH} {YEAR}",
                style: {
                    fontSize: "16pt",
                    fontWeight: "bold",
                    textAlign: "center",
                    fontFamily: "Times New Roman"
                }
            },
            infoTable: {
                columns: ["Report Information", "Executive Summary"],
                rows: [
                    ["Period: {PERIOD}", "Significant Changes Detected: {SIGNIFICANT_COUNT}"],
                    ["Generated at: {GENERATED_AT}", "HIGH Priority Changes: {HIGH_COUNT}"],
                    ["Generated By: {GENERATED_BY}", "MODERATE Priority Changes: {MODERATE_COUNT}"],
                    ["Designation: {DESIGNATION}", "LOW Priority Changes: {LOW_COUNT}"]
                ],
                style: {
                    borderStyle: "single",
                    borderWidth: "1pt",
                    fontSize: "11pt",
                    fontFamily: "Times New Roman"
                }
            },
            sections: {
                findingsAndObservations: {
                    title: "Finding and Observations",
                    style: {
                        fontSize: "12pt",
                        fontWeight: "bold",
                        marginTop: "20pt",
                        marginBottom: "10pt"
                    }
                },
                newEmployees: {
                    title: "NEW EMPLOYEES",
                    subtitle: "The following Employees were added to {PERIOD} Payroll:",
                    style: {
                        fontSize: "11pt",
                        fontWeight: "bold",
                        marginTop: "15pt",
                        marginBottom: "5pt"
                    }
                },
                removedEmployees: {
                    title: "REMOVED EMPLOYEES",
                    subtitle: "The following Employees were removed to {PERIOD} Payroll:",
                    style: {
                        fontSize: "11pt",
                        fontWeight: "bold",
                        marginTop: "15pt",
                        marginBottom: "5pt"
                    }
                }
            },
            footer: {
                text: "Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © {YEAR}",
                style: {
                    fontSize: "9pt",
                    textAlign: "center",
                    marginTop: "30pt",
                    fontFamily: "Times New Roman"
                }
            }
        };
    }

    /**
     * Generate Word document from smart report data
     * @param {Object} smartReport - Smart report data from business rules engine
     * @returns {Object} Word document structure
     */
    generateWordDocument(smartReport) {
        console.log('📄 Generating Word document from smart report...');
        
        const metadata = smartReport.metadata;
        const currentDate = new Date();
        const period = this.extractPeriodFromMetadata(metadata);
        
        // Create document structure
        const document = {
            metadata: {
                title: `PAYROLL AUDIT REPORT: ${period.month} ${period.year}`,
                author: metadata.generatedBy,
                subject: "Payroll Audit Report",
                created: metadata.generatedAt
            },
            content: []
        };

        // Add header
        document.content.push(this.generateHeader(period));
        
        // Add information table
        document.content.push(this.generateInfoTable(smartReport, period));
        
        // Add findings and observations section
        document.content.push(this.generateFindingsSection(smartReport));
        
        // Add new employees section
        if (this.hasNewEmployees(smartReport)) {
            document.content.push(this.generateNewEmployeesSection(smartReport, period));
        }
        
        // Add removed employees section
        if (this.hasRemovedEmployees(smartReport)) {
            document.content.push(this.generateRemovedEmployeesSection(smartReport, period));
        }
        
        // Add footer
        document.content.push(this.generateFooter(period.year));
        
        console.log('✅ Word document structure generated');
        return document;
    }

    /**
     * Generate document header
     */
    generateHeader(period) {
        return {
            type: "paragraph",
            content: `PAYROLL AUDIT REPORT: ${period.month} ${period.year}`,
            style: this.templateStructure.header.style
        };
    }

    /**
     * Generate information table
     */
    generateInfoTable(smartReport, period) {
        const metadata = smartReport.metadata;
        const summary = smartReport.summary;
        
        return {
            type: "table",
            columns: this.templateStructure.infoTable.columns,
            rows: [
                [`Period: ${period.month} ${period.year}`, `Significant Changes Detected: ${summary.totalChanges}`],
                [`Generated at: ${this.formatDateTime(metadata.generatedAt)}`, `HIGH Priority Changes: ${summary.highPriorityChanges}`],
                [`Generated By: ${metadata.generatedBy}`, `MODERATE Priority Changes: ${summary.moderatePriorityChanges}`],
                [`Designation: ${metadata.designation}`, `LOW Priority Changes: ${summary.lowPriorityChanges}`]
            ],
            style: this.templateStructure.infoTable.style
        };
    }

    /**
     * Generate findings and observations section
     */
    generateFindingsSection(smartReport) {
        const findings = [];
        
        // Add section header
        findings.push({
            type: "paragraph",
            content: "Finding and Observations",
            style: this.templateStructure.sections.findingsAndObservations.style
        });

        // Process high priority changes first
        const highPriorityChanges = smartReport.findings.highPriorityChanges;
        if (highPriorityChanges.length > 0) {
            findings.push(...this.generateChangeFindings(highPriorityChanges));
        }

        // Process moderate priority changes
        const moderatePriorityChanges = smartReport.findings.moderatePriorityChanges;
        if (moderatePriorityChanges.length > 0) {
            findings.push(...this.generateChangeFindings(moderatePriorityChanges));
        }

        // Process promotions if any
        if (smartReport.specialFindings.promotions.length > 0) {
            findings.push(...this.generatePromotionFindings(smartReport.specialFindings.promotions));
        }

        return {
            type: "section",
            content: findings
        };
    }

    /**
     * Generate change findings in the exact format from template
     */
    generateChangeFindings(changes) {
        const findings = [];
        
        // Group changes by employee
        const employeeGroups = this.groupChangesByEmployee(changes);
        
        Object.entries(employeeGroups).forEach(([employeeKey, empChanges]) => {
            const employee = empChanges[0];
            
            // Employee header (e.g., "COP2626: SAMUEL ASIEDU – AUDIT, MONITORING & EVALUATION")
            findings.push({
                type: "paragraph",
                content: `${employee.employee_id}: ${employee.employee_name} – ${this.extractDepartment(employee)}`,
                style: {
                    fontSize: "11pt",
                    fontWeight: "bold",
                    marginTop: "10pt",
                    marginBottom: "5pt",
                    textIndent: "20pt"
                }
            });

            // Add numbered findings for this employee
            empChanges.forEach((change, index) => {
                const findingText = this.formatChangeFinding(change);
                findings.push({
                    type: "paragraph",
                    content: `${index + 1}. ${findingText}`,
                    style: {
                        fontSize: "11pt",
                        marginLeft: "40pt",
                        marginBottom: "5pt",
                        textIndent: "20pt"
                    }
                });
            });
        });

        return findings;
    }

    /**
     * Format individual change finding
     */
    formatChangeFinding(change) {
        const itemName = change.item_name || change.item_label;
        const prevValue = this.formatValue(change.previous_value);
        const currValue = this.formatValue(change.current_value);
        const changeType = change.category || change.change_type;
        
        switch (changeType.toUpperCase()) {
            case 'INCREASE':
                return `${itemName} increased from ${prevValue} to ${currValue} in ${this.getCurrentPeriod()} (increase of ${this.calculateDifference(change.previous_value, change.current_value)})`;
            
            case 'DECREASE':
                return `${itemName} decreased from ${prevValue} to ${currValue} in ${this.getCurrentPeriod()} (decrease of ${this.calculateDifference(change.previous_value, change.current_value)})`;
            
            case 'NEW':
                return `NEW ${itemName} of ${currValue} is added to Payslip for ${this.getCurrentPeriod()}.`;
            
            case 'REMOVED':
                return `${itemName} of ${prevValue} was removed from Payslip for ${this.getCurrentPeriod()}.`;
            
            default:
                return `${itemName} changed from ${prevValue} to ${currValue} in ${this.getCurrentPeriod()}.`;
        }
    }

    /**
     * Generate new employees section
     */
    generateNewEmployeesSection(smartReport, period) {
        const newEmployees = this.extractNewEmployees(smartReport);
        
        const section = [];
        
        // Section header
        section.push({
            type: "paragraph",
            content: "NEW EMPLOYEES",
            style: this.templateStructure.sections.newEmployees.style
        });
        
        // Subtitle
        section.push({
            type: "paragraph",
            content: `The following Employees were added to ${period.month} ${period.year} Payroll:`,
            style: {
                fontSize: "11pt",
                marginBottom: "10pt"
            }
        });
        
        // Employee list
        newEmployees.forEach(employee => {
            section.push({
                type: "paragraph",
                content: `• ${employee.employee_id}: ${employee.employee_name} - ${this.extractDepartment(employee)}`,
                style: {
                    fontSize: "11pt",
                    marginLeft: "20pt",
                    marginBottom: "3pt"
                }
            });
        });
        
        return {
            type: "section",
            content: section
        };
    }

    /**
     * Generate removed employees section
     */
    generateRemovedEmployeesSection(smartReport, period) {
        const removedEmployees = this.extractRemovedEmployees(smartReport);
        
        const section = [];
        
        // Section header
        section.push({
            type: "paragraph",
            content: "REMOVED EMPLOYEES",
            style: this.templateStructure.sections.removedEmployees.style
        });
        
        // Subtitle
        section.push({
            type: "paragraph",
            content: `The following Employees were removed to ${period.month} ${period.year} Payroll:`,
            style: {
                fontSize: "11pt",
                marginBottom: "10pt"
            }
        });
        
        // Employee list
        removedEmployees.forEach(employee => {
            section.push({
                type: "paragraph",
                content: `• ${employee.employee_id}: ${employee.employee_name} - ${this.extractDepartment(employee)}`,
                style: {
                    fontSize: "11pt",
                    marginLeft: "20pt",
                    marginBottom: "3pt"
                }
            });
        });
        
        return {
            type: "section",
            content: section
        };
    }

    /**
     * Generate footer
     */
    generateFooter(year) {
        return {
            type: "paragraph",
            content: `Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © ${year}`,
            style: this.templateStructure.footer.style
        };
    }

    // HELPER METHODS

    extractPeriodFromMetadata(metadata) {
        const date = new Date(metadata.generatedAt);
        return {
            month: date.toLocaleString('default', { month: 'long' }).toUpperCase(),
            year: date.getFullYear()
        };
    }

    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('en-GB', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    groupChangesByEmployee(changes) {
        return changes.reduce((groups, change) => {
            const key = `${change.employee_id}-${change.employee_name}`;
            if (!groups[key]) groups[key] = [];
            groups[key].push(change);
            return groups;
        }, {});
    }

    extractDepartment(employee) {
        // Extract department from section name or use a default
        const sectionName = employee.section_name || '';
        if (sectionName.toLowerCase().includes('minister')) return 'MINISTERS';
        if (sectionName.toLowerCase().includes('staff')) return 'STAFF';
        return 'GENERAL';
    }

    formatValue(value) {
        if (value === null || value === undefined || value === '') return '0.00';
        const numValue = parseFloat(value);
        if (isNaN(numValue)) return value;
        return numValue.toFixed(2);
    }

    calculateDifference(prevValue, currValue) {
        const prev = parseFloat(prevValue) || 0;
        const curr = parseFloat(currValue) || 0;
        return Math.abs(curr - prev).toFixed(2);
    }

    getCurrentPeriod() {
        const date = new Date();
        return `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
    }

    hasNewEmployees(smartReport) {
        return this.extractNewEmployees(smartReport).length > 0;
    }

    hasRemovedEmployees(smartReport) {
        return this.extractRemovedEmployees(smartReport).length > 0;
    }

    extractNewEmployees(smartReport) {
        return smartReport.processedChanges.filter(change => 
            change.category === 'NEW' && 
            change.section_name?.toLowerCase().includes('personal')
        );
    }

    extractRemovedEmployees(smartReport) {
        return smartReport.processedChanges.filter(change => 
            change.category === 'REMOVED' && 
            change.section_name?.toLowerCase().includes('personal')
        );
    }

    generatePromotionFindings(promotions) {
        const findings = [];
        
        promotions.forEach(promotion => {
            findings.push({
                type: "paragraph",
                content: `${promotion.employeeId}: ${promotion.employeeName} – PROMOTION`,
                style: {
                    fontSize: "11pt",
                    fontWeight: "bold",
                    marginTop: "10pt",
                    marginBottom: "5pt",
                    textIndent: "20pt"
                }
            });
            
            findings.push({
                type: "paragraph",
                content: `1. Salary increase of ${this.formatValue(promotion.salaryIncrease)} detected for ${this.getCurrentPeriod()}.`,
                style: {
                    fontSize: "11pt",
                    marginLeft: "40pt",
                    marginBottom: "5pt",
                    textIndent: "20pt"
                }
            });
        });
        
        return findings;
    }
}

    /**
     * Convert document structure to HTML for Word generation
     * @param {Object} document - Document structure
     * @returns {String} HTML content for Word document
     */
    convertToWordHTML(document) {
        let html = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>${document.metadata.title}</title>
    <style>
        body {
            font-family: 'Times New Roman', serif;
            font-size: 11pt;
            line-height: 1.2;
            margin: 1in;
            color: black;
        }
        .header {
            text-align: center;
            font-size: 16pt;
            font-weight: bold;
            margin-bottom: 20pt;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20pt;
        }
        .info-table td {
            border: 1pt solid black;
            padding: 8pt;
            font-size: 11pt;
            vertical-align: top;
        }
        .info-table .header-cell {
            font-weight: bold;
            background-color: #f0f0f0;
        }
        .section-title {
            font-size: 12pt;
            font-weight: bold;
            margin-top: 20pt;
            margin-bottom: 10pt;
        }
        .subsection-title {
            font-size: 11pt;
            font-weight: bold;
            margin-top: 15pt;
            margin-bottom: 5pt;
        }
        .employee-header {
            font-size: 11pt;
            font-weight: bold;
            margin-top: 10pt;
            margin-bottom: 5pt;
            text-indent: 20pt;
        }
        .finding-item {
            font-size: 11pt;
            margin-left: 40pt;
            margin-bottom: 5pt;
            text-indent: 20pt;
        }
        .employee-list-item {
            font-size: 11pt;
            margin-left: 20pt;
            margin-bottom: 3pt;
        }
        .footer {
            text-align: center;
            font-size: 9pt;
            margin-top: 30pt;
            border-top: 1pt solid black;
            padding-top: 10pt;
        }
    </style>
</head>
<body>`;

        // Process each content item
        document.content.forEach(item => {
            html += this.renderContentItem(item);
        });

        html += `
</body>
</html>`;

        return html;
    }

    /**
     * Render individual content item to HTML
     */
    renderContentItem(item) {
        switch (item.type) {
            case 'paragraph':
                return this.renderParagraph(item);
            case 'table':
                return this.renderTable(item);
            case 'section':
                return this.renderSection(item);
            default:
                return '';
        }
    }

    renderParagraph(item) {
        const className = this.getClassFromStyle(item.style);
        return `<p class="${className}">${item.content}</p>\n`;
    }

    renderTable(item) {
        let html = '<table class="info-table">\n';

        // Header row
        html += '<tr>\n';
        item.columns.forEach(col => {
            html += `<td class="header-cell">${col}</td>\n`;
        });
        html += '</tr>\n';

        // Data rows
        item.rows.forEach(row => {
            html += '<tr>\n';
            row.forEach(cell => {
                html += `<td>${cell}</td>\n`;
            });
            html += '</tr>\n';
        });

        html += '</table>\n';
        return html;
    }

    renderSection(item) {
        let html = '';
        item.content.forEach(subItem => {
            html += this.renderContentItem(subItem);
        });
        return html;
    }

    getClassFromStyle(style) {
        if (!style) return '';

        if (style.fontSize === '16pt' && style.fontWeight === 'bold') return 'header';
        if (style.fontSize === '12pt' && style.fontWeight === 'bold') return 'section-title';
        if (style.fontSize === '11pt' && style.fontWeight === 'bold' && style.textIndent) return 'employee-header';
        if (style.fontSize === '11pt' && style.fontWeight === 'bold') return 'subsection-title';
        if (style.marginLeft === '40pt') return 'finding-item';
        if (style.marginLeft === '20pt') return 'employee-list-item';
        if (style.textAlign === 'center' && style.fontSize === '9pt') return 'footer';

        return '';
    }

    /**
     * Generate downloadable Word document
     * @param {Object} smartReport - Smart report data
     * @returns {Promise} Promise that resolves with download URL
     */
    async generateDownloadableWord(smartReport) {
        console.log('📄 Generating downloadable Word document...');

        try {
            // Generate document structure
            const document = this.generateWordDocument(smartReport);

            // Convert to HTML
            const htmlContent = this.convertToWordHTML(document);

            // Create blob with Word MIME type
            const blob = new Blob([htmlContent], {
                type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            });

            // Create download URL
            const url = URL.createObjectURL(blob);

            // Generate filename
            const period = this.extractPeriodFromMetadata(smartReport.metadata);
            const filename = `Payroll_Audit_Report_${period.month}_${period.year}.doc`;

            console.log('✅ Word document generated successfully');

            return {
                url,
                filename,
                size: blob.size
            };

        } catch (error) {
            console.error('❌ Error generating Word document:', error);
            throw error;
        }
    }
}

// Export for use in Final Report Interface
console.log('📄 Exporting WordTemplateEngine to window...');
window.WordTemplateEngine = WordTemplateEngine;
console.log('✅ WordTemplateEngine exported successfully:', typeof window.WordTemplateEngine);
