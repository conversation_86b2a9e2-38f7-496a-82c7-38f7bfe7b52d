/**
 * ITEM-BASED REPORT GENERATOR
 * Implements item-first reporting format from specification
 * Groups changes by payroll items and lists affected employees
 */

class ItemBasedReportGenerator {
    constructor() {
        this.templateFormat = this.initializeTemplateFormat();
        console.log('📋 Item-Based Report Generator initialized');
    }

    /**
     * Initialize template format for item-based reporting
     */
    initializeTemplateFormat() {
        return {
            // Font specifications from FINAL REPORT.txt
            fonts: {
                body: 'Cambria (Body)',
                bodySize: 14,
                heading: '<PERSON><PERSON><PERSON> (Headings)', 
                headingSize: 26
            },
            
            // Item-based structure from specification
            structure: {
                title: 'PAYROLL AUDIT REPORT: {MONTH} {YEAR}',
                format: 'item_first',
                sections: [
                    'reportInformation',
                    'executiveSummary', 
                    'itemFindings'
                ]
            },
            
            // Item format from specification: "ITEM increased in MONTH Payslip for the following:"
            itemFormat: {
                template: '{item_name} {change_type} in {month} Payslip for the following:',
                employeeFormat: '•{employee_id}: {employee_name} – {department}'
            }
        };
    }

    /**
     * Generate Item-Based Report according to specification
     * @param {Object} intelligentAnalysisResults - Results from BusinessRulesEngine
     * @param {Object} reportConfig - Report configuration (Generated By, Designation)
     * @returns {Object} Generated report data for Word document creation
     */
    generateItemBasedReport(intelligentAnalysisResults, reportConfig) {
        console.log('📋 Generating Item-Based Report with specification compliance...');
        
        const reportData = {
            metadata: this.generateReportMetadata(intelligentAnalysisResults, reportConfig),
            reportInformation: this.generateReportInformationTable(intelligentAnalysisResults, reportConfig),
            executiveSummary: this.generateExecutiveSummaryTable(intelligentAnalysisResults),
            itemFindings: this.generateItemFindingsSection(intelligentAnalysisResults)
        };
        
        console.log('✅ Item-Based Report data generated');
        return reportData;
    }

    /**
     * Generate report metadata
     */
    generateReportMetadata(results, config) {
        const currentDate = new Date();
        const month = currentDate.toLocaleString('default', { month: 'long' }).toUpperCase();
        const year = currentDate.getFullYear();
        
        return {
            title: `PAYROLL AUDIT REPORT: ${month} ${year}`,
            reportPeriod: `${month} ${year}`,
            generatedAt: currentDate.toLocaleString('en-US', {
                year: 'numeric',
                month: '2-digit', 
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            }),
            generatedBy: config.generatedBy || 'System Generated',
            designation: config.designation || 'Audit Officer',
            month,
            year
        };
    }

    /**
     * Generate Report Information Table
     */
    generateReportInformationTable(results, config) {
        const metadata = this.generateReportMetadata(results, config);
        
        return {
            'Report Period': metadata.reportPeriod,
            'Generated at': metadata.generatedAt,
            'Generated By': metadata.generatedBy,
            'Designation': metadata.designation
        };
    }

    /**
     * Generate Executive Summary Table
     */
    generateExecutiveSummaryTable(results) {
        const summary = results.executiveSummary;
        
        return {
            'Significant Changes Detected': summary.significantChangesDetected || 0,
            'HIGH Priority Changes': summary.highPriorityChanges || 0,
            'MODERATE Priority Changes': summary.moderatePriorityChanges || 0,
            'LOW Priority Changes': summary.lowPriorityChanges || 0
        };
    }

    /**
     * Generate Item Findings Section (Main content for item-based format)
     * Format: "ITEM increased in MONTH Payslip for the following:"
     * Followed by bullet list of affected employees
     */
    generateItemFindingsSection(results) {
        const itemFindings = [];
        
        // Process item-based data according to specification format
        if (results.itemBasedData && results.itemBasedData.length > 0) {
            results.itemBasedData.forEach(item => {
                if (item.affectedEmployees && item.affectedEmployees.length > 0) {
                    const itemSection = {
                        itemName: item.itemName,
                        sectionName: item.sectionName,
                        changeType: item.changeType,
                        narration: item.narration,
                        affectedEmployees: item.affectedEmployees.map(emp => ({
                            employeeId: emp.employeeId,
                            employeeName: emp.employeeName,
                            department: emp.department,
                            formatted: `•${emp.employeeId}: ${emp.employeeName} – ${emp.department}`,
                            changeDetails: emp.changeDetails
                        })),
                        employeeCount: item.employeeCount
                    };
                    
                    itemFindings.push(itemSection);
                }
            });
        }
        
        // Sort by item name for consistent reporting
        return itemFindings.sort((a, b) => a.itemName.localeCompare(b.itemName));
    }

    /**
     * Generate item section header according to specification
     * Format: "SECURITY GUARD ALLOWANCE increased in July 2025 Payslip for the following:"
     */
    generateItemSectionHeader(item, month) {
        const changeTypeText = this.getChangeTypeText(item.changeType);
        return `${item.itemName} ${changeTypeText} in ${month} Payslip for the following:`;
    }

    /**
     * Convert change type to readable text
     */
    getChangeTypeText(changeType) {
        const changeTypeMap = {
            'INCREASED': 'increased',
            'DECREASED': 'decreased', 
            'NEW': 'was added',
            'REMOVED': 'was removed'
        };
        
        return changeTypeMap[changeType] || changeType.toLowerCase();
    }

    /**
     * Group changes by item for item-based reporting
     */
    groupChangesByItem(changes) {
        const itemGroups = {};
        
        changes.forEach(change => {
            const key = `${change.section_name}_${change.item_name}`;
            
            if (!itemGroups[key]) {
                itemGroups[key] = {
                    itemName: change.item_name,
                    sectionName: change.section_name,
                    changeType: change.changeType,
                    changes: []
                };
            }
            
            itemGroups[key].changes.push(change);
        });
        
        return itemGroups;
    }

    /**
     * Generate summary statistics for item-based report
     */
    generateItemSummaryStats(itemFindings) {
        const stats = {
            totalItems: itemFindings.length,
            totalEmployeesAffected: 0,
            changeTypeBreakdown: {
                INCREASED: 0,
                DECREASED: 0,
                NEW: 0,
                REMOVED: 0
            },
            sectionBreakdown: {}
        };
        
        itemFindings.forEach(item => {
            stats.totalEmployeesAffected += item.employeeCount;
            stats.changeTypeBreakdown[item.changeType] = (stats.changeTypeBreakdown[item.changeType] || 0) + 1;
            
            const section = item.sectionName;
            stats.sectionBreakdown[section] = (stats.sectionBreakdown[section] || 0) + 1;
        });
        
        return stats;
    }

    /**
     * Validate item-based report data
     */
    validateReportData(reportData) {
        const errors = [];
        
        if (!reportData.metadata?.title) {
            errors.push('Missing report title');
        }
        
        if (!reportData.reportInformation?.['Generated By']) {
            errors.push('Missing Generated By information');
        }
        
        if (!reportData.reportInformation?.['Designation']) {
            errors.push('Missing Designation information');
        }
        
        if (!reportData.itemFindings || reportData.itemFindings.length === 0) {
            errors.push('No item findings to report');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Get template format for Word document generation
     */
    getTemplateFormat() {
        return this.templateFormat;
    }

    /**
     * Generate item-based report preview for UI
     */
    generateReportPreview(reportData) {
        const preview = {
            title: reportData.metadata.title,
            summary: `${reportData.itemFindings.length} items with changes affecting multiple employees`,
            sections: reportData.itemFindings.map(item => ({
                title: item.itemName,
                description: item.narration,
                employeeCount: item.employeeCount,
                changeType: item.changeType
            }))
        };
        
        return preview;
    }
}

// Export for use in Final Report Interface
window.ItemBasedReportGenerator = ItemBasedReportGenerator;
