#!/usr/bin/env python3
"""
Comprehensive Fix for Auto-Learning Approval System
Addresses both database transfer and UI loading issues
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    return None

def fix_database_transfer_issue():
    """Fix the database transfer issue - transfer approved items to dictionary_items"""
    print("🔧 FIXING DATABASE TRANSFER ISSUE")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 1. Find approved items that need to be transferred
        print("📊 1. FINDING APPROVED ITEMS TO TRANSFER:")
        
        cursor.execute('''
            SELECT a.id, a.section_name, a.item_label, a.confidence_score, a.session_id
            FROM auto_learning_results a
            LEFT JOIN dictionary_items d ON (
                d.item_name = a.item_label AND 
                d.section_id = (SELECT id FROM dictionary_sections WHERE section_name = a.section_name)
            )
            WHERE a.auto_approved = 1 AND a.dictionary_updated = 1 AND d.id IS NULL
        ''')
        
        items_to_transfer = cursor.fetchall()
        print(f"   Found {len(items_to_transfer)} approved items to transfer")
        
        if len(items_to_transfer) == 0:
            print("✅ No items need to be transferred")
            conn.close()
            return True
        
        # 2. Transfer each approved item to dictionary_items
        print("\n📊 2. TRANSFERRING APPROVED ITEMS:")
        
        transferred_count = 0
        failed_count = 0
        
        for item in items_to_transfer:
            auto_id, section_name, item_label, confidence_score, session_id = item
            
            try:
                # Get section_id
                cursor.execute('SELECT id FROM dictionary_sections WHERE section_name = ?', (section_name,))
                section_result = cursor.fetchone()
                
                if not section_result:
                    print(f"   ❌ Section '{section_name}' not found for item '{item_label}'")
                    failed_count += 1
                    continue
                
                section_id = section_result[0]
                
                # Insert into dictionary_items with auto-learned metadata
                cursor.execute('''
                    INSERT INTO dictionary_items (
                        section_id, item_name, standard_key, format_type, value_format,
                        include_in_report, is_fixed, validation_rules, created_at, updated_at,
                        auto_learned, source_session, confidence_score,
                        include_new, include_increase, include_decrease, include_removed, include_no_change
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    section_id,
                    item_label,
                    item_label,  # standard_key = item_name for auto-learned items
                    'text',      # default format_type
                    'text',      # default value_format
                    1,           # include_in_report = True
                    0,           # is_fixed = False (auto-learned items are not fixed)
                    '{}',        # empty validation_rules
                    datetime.now().isoformat(),  # created_at
                    datetime.now().isoformat(),  # updated_at
                    1,           # auto_learned = True
                    session_id,  # source_session
                    confidence_score,  # confidence_score
                    1,           # include_new = True (default)
                    1,           # include_increase = True (default)
                    1,           # include_decrease = True (default)
                    1,           # include_removed = True (default)
                    0            # include_no_change = False (default)
                ))
                
                transferred_count += 1
                print(f"   ✅ Transferred: {section_name}.{item_label}")
                
            except Exception as e:
                print(f"   ❌ Failed to transfer {section_name}.{item_label}: {e}")
                failed_count += 1
        
        conn.commit()
        
        print(f"\n📊 TRANSFER RESULTS:")
        print(f"   ✅ Successfully transferred: {transferred_count}")
        print(f"   ❌ Failed transfers: {failed_count}")
        
        conn.close()
        return transferred_count > 0
        
    except Exception as e:
        print(f"❌ Error fixing database transfer: {e}")
        return False

def fix_ui_loading_mechanism():
    """Fix the UI loading mechanism to include auto-learned metadata"""
    print("\n🔧 FIXING UI LOADING MECHANISM")
    print("=" * 60)
    
    try:
        unified_db_path = "core/unified_database.js"
        
        if not os.path.exists(unified_db_path):
            print("❌ unified_database.js not found")
            return False
        
        # Read the current file
        with open(unified_db_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if auto-learned metadata is already included
        if 'auto_learned:' in content and 'source_session:' in content and 'confidence_score:' in content:
            print("✅ Auto-learned metadata already included in loadDictionary method")
            return True
        
        # Find the itemData object in loadDictionary method
        target_section = '''                    // CRITICAL FIX: Load change detection toggle states from database
                    include_new: item.include_new === 1,
                    include_increase: item.include_increase === 1,
                    include_decrease: item.include_decrease === 1,
                    include_removed: item.include_removed === 1,
                    include_no_change: item.include_no_change === 1
                };'''
        
        replacement_section = '''                    // CRITICAL FIX: Load change detection toggle states from database
                    include_new: item.include_new === 1,
                    include_increase: item.include_increase === 1,
                    include_decrease: item.include_decrease === 1,
                    include_removed: item.include_removed === 1,
                    include_no_change: item.include_no_change === 1,
                    // AUTO-LEARNING FIX: Load auto-learned metadata
                    auto_learned: item.auto_learned === 1,
                    source_session: item.source_session || null,
                    confidence_score: item.confidence_score || 0
                };'''
        
        if target_section in content:
            # Replace the section
            new_content = content.replace(target_section, replacement_section)
            
            # Write the updated content
            with open(unified_db_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ Successfully updated unified_database.js to include auto-learned metadata")
            print("   Added: auto_learned, source_session, confidence_score fields")
            return True
        else:
            print("❌ Could not find target section in unified_database.js")
            print("   Manual update may be required")
            return False
        
    except Exception as e:
        print(f"❌ Error fixing UI loading mechanism: {e}")
        return False

def verify_fix():
    """Verify that the fix is working correctly"""
    print("\n🔍 VERIFYING FIX")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check auto-learned items in dictionary_items
        cursor.execute('SELECT COUNT(*) FROM dictionary_items WHERE auto_learned = 1')
        auto_learned_count = cursor.fetchone()[0]
        
        print(f"📊 VERIFICATION RESULTS:")
        print(f"   Auto-learned items in dictionary_items: {auto_learned_count}")
        
        if auto_learned_count > 0:
            # Show some examples
            cursor.execute('''
                SELECT ds.section_name, di.item_name, di.confidence_score, di.source_session
                FROM dictionary_items di
                JOIN dictionary_sections ds ON di.section_id = ds.id
                WHERE di.auto_learned = 1
                ORDER BY di.created_at DESC
                LIMIT 5
            ''')
            
            examples = cursor.fetchall()
            print(f"   ✅ Example auto-learned items:")
            for example in examples:
                section, item, confidence, session = example
                print(f"     {section}.{item} (Confidence: {confidence}, Session: {session})")
        
        # Check unified_database.js
        unified_db_path = "core/unified_database.js"
        if os.path.exists(unified_db_path):
            with open(unified_db_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'auto_learned:' in content:
                print(f"   ✅ unified_database.js includes auto_learned metadata")
            else:
                print(f"   ❌ unified_database.js missing auto_learned metadata")
        
        conn.close()
        
        success = auto_learned_count > 0
        
        if success:
            print(f"\n🎉 FIX VERIFICATION SUCCESSFUL!")
            print(f"   {auto_learned_count} auto-learned items are now available in Dictionary Manager")
        else:
            print(f"\n❌ FIX VERIFICATION FAILED!")
            print(f"   No auto-learned items found in dictionary_items table")
        
        return success
        
    except Exception as e:
        print(f"❌ Error verifying fix: {e}")
        return False

def main():
    """Main function to run the comprehensive fix"""
    print("🎯 COMPREHENSIVE AUTO-LEARNING APPROVAL SYSTEM FIX")
    print("=" * 80)
    print("Fixing issues:")
    print("1. Database transfer: Approved items not transferred to dictionary_items")
    print("2. UI loading: Auto-learned metadata not loaded in Dictionary Manager")
    print("3. Real-time updates: Approved items not appearing immediately")
    print("=" * 80)
    
    # Step 1: Fix database transfer issue
    transfer_success = fix_database_transfer_issue()
    
    # Step 2: Fix UI loading mechanism
    loading_success = fix_ui_loading_mechanism()
    
    # Step 3: Verify the fix
    verification_success = verify_fix()
    
    overall_success = transfer_success and loading_success and verification_success
    
    print(f"\n{'='*80}")
    if overall_success:
        print("🎉 COMPREHENSIVE FIX COMPLETED SUCCESSFULLY!")
        print("✅ Database transfer issue resolved")
        print("✅ UI loading mechanism updated")
        print("✅ Auto-learned items now appear in Dictionary Manager")
        print("\n📋 NEXT STEPS:")
        print("   1. Restart the Templar Payroll Auditor application")
        print("   2. Open Dictionary Manager to see auto-learned items")
        print("   3. Test auto-learning approval workflow")
        print("   4. Verify real-time updates work correctly")
    else:
        print("❌ COMPREHENSIVE FIX FAILED!")
        print("🔧 Please review the errors above and apply manual fixes if needed")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
