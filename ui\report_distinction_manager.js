/**
 * REPORT DISTINCTION MANAGER
 * Ensures clear distinction between Pre-Report and Final Report functionality
 * Implements specification requirements for dual reporting system
 */

class ReportDistinctionManager {
    constructor() {
        this.reportTypes = this.initializeReportTypes();
        this.currentMode = 'pre-reporting'; // 'pre-reporting' or 'smart-reporter'
        this.capabilities = this.initializeCapabilities();
        
        console.log('🔄 Report Distinction Manager initialized');
    }

    /**
     * Initialize report type definitions
     */
    initializeReportTypes() {
        return {
            preReport: {
                name: 'PRE-REPORT',
                description: 'Traditional data source reporting',
                functionality: 'Basic data processing and traditional report generation',
                buttonText: 'Generate PRE-REPORT',
                buttonClass: 'btn secondary pre-report-btn',
                icon: '📋',
                features: [
                    'Basic data filtering',
                    'Simple change listing',
                    'Traditional formatting',
                    'Quick generation',
                    'Standard templates'
                ],
                limitations: [
                    'No intelligent analysis',
                    'No business rules application',
                    'No automatic categorization',
                    'No promotion/transfer detection',
                    'Basic formatting only'
                ]
            },
            finalReport: {
                name: 'FINAL-REPORT',
                description: 'Intelligent Smart Reporter with business rules',
                functionality: 'Algorithm-powered intelligent analysis with business rules engine',
                buttonText: 'Generate FINAL-REPORT',
                buttonClass: 'btn primary final-report-btn',
                icon: '📄',
                features: [
                    'Intelligent business rules analysis',
                    'Automatic promotion detection',
                    'Transfer identification',
                    'NEW/REMOVED employee detection',
                    'Loan reporting rules',
                    'Specification-compliant formatting',
                    'Employee-based and Item-based reports',
                    'Automatic appendix generation',
                    'Executive summary with statistics',
                    'Multiple output formats (Word, PDF, Excel)'
                ],
                requirements: [
                    'Generated By field required',
                    'Designation field required',
                    'Report type selection',
                    'Output format selection'
                ]
            }
        };
    }

    /**
     * Initialize capability definitions
     */
    initializeCapabilities() {
        return {
            preReporting: {
                dataProcessing: 'basic',
                businessRules: false,
                intelligentAnalysis: false,
                promotionDetection: false,
                transferDetection: false,
                employeeDetection: false,
                loanAnalysis: false,
                appendixGeneration: false,
                executiveSummary: 'basic',
                outputFormats: ['basic'],
                configurationRequired: false
            },
            smartReporter: {
                dataProcessing: 'advanced',
                businessRules: true,
                intelligentAnalysis: true,
                promotionDetection: true,
                transferDetection: true,
                employeeDetection: true,
                loanAnalysis: true,
                appendixGeneration: true,
                executiveSummary: 'comprehensive',
                outputFormats: ['word', 'pdf', 'excel'],
                configurationRequired: true
            }
        };
    }

    /**
     * Switch between Pre-Reporting and Smart Reporter modes
     */
    switchMode(mode) {
        if (!['pre-reporting', 'smart-reporter'].includes(mode)) {
            console.error('❌ Invalid mode:', mode);
            return false;
        }

        this.currentMode = mode;
        this.updateUIForMode(mode);
        
        console.log(`🔄 Switched to ${mode} mode`);
        return true;
    }

    /**
     * Update UI based on current mode
     */
    updateUIForMode(mode) {
        // Update mode indicator
        this.updateModeIndicator(mode);
        
        // Show/hide relevant sections
        this.toggleModeSections(mode);
        
        // Update button states
        this.updateButtonStates(mode);
        
        // Update feature descriptions
        this.updateFeatureDescriptions(mode);
    }

    /**
     * Update mode indicator
     */
    updateModeIndicator(mode) {
        const indicator = document.getElementById('report-mode-indicator');
        if (!indicator) return;

        const modeConfig = mode === 'pre-reporting' ? this.reportTypes.preReport : this.reportTypes.finalReport;
        
        indicator.innerHTML = `
            <div class="mode-indicator ${mode}">
                <div class="mode-icon">${modeConfig.icon}</div>
                <div class="mode-info">
                    <h4>${modeConfig.name}</h4>
                    <p>${modeConfig.description}</p>
                </div>
            </div>
        `;
    }

    /**
     * Toggle sections based on mode
     */
    toggleModeSections(mode) {
        // Pre-reporting specific sections
        const preReportingSections = document.querySelectorAll('.pre-reporting-only');
        preReportingSections.forEach(section => {
            section.style.display = mode === 'pre-reporting' ? 'block' : 'none';
        });

        // Smart reporter specific sections
        const smartReporterSections = document.querySelectorAll('.smart-reporter-only');
        smartReporterSections.forEach(section => {
            section.style.display = mode === 'smart-reporter' ? 'block' : 'none';
        });

        // Configuration panel visibility
        const configPanel = document.querySelector('.dual-report-configuration');
        if (configPanel) {
            const smartReporterFields = configPanel.querySelectorAll('.smart-reporter-only');
            smartReporterFields.forEach(field => {
                field.style.display = mode === 'smart-reporter' ? 'block' : 'none';
            });
        }
    }

    /**
     * Update button states based on mode
     */
    updateButtonStates(mode) {
        const preReportBtn = document.querySelector('.pre-report-btn');
        const finalReportBtn = document.querySelector('.final-report-btn');

        if (mode === 'pre-reporting') {
            if (preReportBtn) {
                preReportBtn.style.display = 'inline-block';
                preReportBtn.disabled = false;
            }
            if (finalReportBtn) {
                finalReportBtn.style.display = 'none';
            }
        } else {
            if (preReportBtn) {
                preReportBtn.style.display = 'none';
            }
            if (finalReportBtn) {
                finalReportBtn.style.display = 'inline-block';
                // Final report button state depends on configuration
                this.updateFinalReportButtonState();
            }
        }
    }

    /**
     * Update final report button state based on configuration
     */
    updateFinalReportButtonState() {
        const finalReportBtn = document.querySelector('.final-report-btn');
        if (!finalReportBtn) return;

        // Check if configuration is complete
        const dualConfig = window.dualReportConfiguration;
        if (dualConfig && dualConfig.isValidForReportGeneration()) {
            finalReportBtn.disabled = false;
            finalReportBtn.title = 'Generate intelligent final report with business rules';
        } else {
            finalReportBtn.disabled = true;
            finalReportBtn.title = 'Please complete report configuration (Generated By and Designation)';
        }
    }

    /**
     * Update feature descriptions
     */
    updateFeatureDescriptions(mode) {
        const featuresContainer = document.getElementById('report-features');
        if (!featuresContainer) return;

        const modeConfig = mode === 'pre-reporting' ? this.reportTypes.preReport : this.reportTypes.finalReport;
        
        featuresContainer.innerHTML = `
            <div class="features-section">
                <h5>Available Features:</h5>
                <ul class="features-list">
                    ${modeConfig.features.map(feature => `<li><i class="fas fa-check"></i> ${feature}</li>`).join('')}
                </ul>
                
                ${modeConfig.limitations ? `
                    <h5>Limitations:</h5>
                    <ul class="limitations-list">
                        ${modeConfig.limitations.map(limitation => `<li><i class="fas fa-times"></i> ${limitation}</li>`).join('')}
                    </ul>
                ` : ''}
                
                ${modeConfig.requirements ? `
                    <h5>Requirements:</h5>
                    <ul class="requirements-list">
                        ${modeConfig.requirements.map(requirement => `<li><i class="fas fa-exclamation-circle"></i> ${requirement}</li>`).join('')}
                    </ul>
                ` : ''}
            </div>
        `;
    }

    /**
     * Validate report generation capability
     */
    validateReportGeneration(reportType, selectedChanges, configuration = null) {
        const validation = {
            canGenerate: false,
            errors: [],
            warnings: []
        };

        // Basic validation
        if (!selectedChanges || selectedChanges.length === 0) {
            validation.errors.push('No changes selected for report generation');
        }

        if (reportType === 'final-report') {
            // Final report specific validation
            if (!configuration) {
                validation.errors.push('Configuration required for final report generation');
            } else {
                if (!configuration.generatedBy || configuration.generatedBy.trim() === '') {
                    validation.errors.push('Generated By field is required for final reports');
                }
                
                if (!configuration.designation || configuration.designation.trim() === '') {
                    validation.errors.push('Designation field is required for final reports');
                }
                
                if (!configuration.reportType) {
                    validation.errors.push('Report type selection required');
                }
                
                if (!configuration.outputFormat) {
                    validation.errors.push('Output format selection required');
                }
            }
        }

        validation.canGenerate = validation.errors.length === 0;
        
        return validation;
    }

    /**
     * Get capability comparison
     */
    getCapabilityComparison() {
        return {
            preReporting: this.capabilities.preReporting,
            smartReporter: this.capabilities.smartReporter,
            differences: this.getCapabilityDifferences()
        };
    }

    /**
     * Get capability differences
     */
    getCapabilityDifferences() {
        const differences = [];
        
        Object.keys(this.capabilities.smartReporter).forEach(capability => {
            const preValue = this.capabilities.preReporting[capability];
            const smartValue = this.capabilities.smartReporter[capability];
            
            if (preValue !== smartValue) {
                differences.push({
                    capability,
                    preReporting: preValue,
                    smartReporter: smartValue,
                    enhancement: smartValue === true || smartValue === 'advanced' || smartValue === 'comprehensive'
                });
            }
        });
        
        return differences;
    }

    /**
     * Generate mode toggle UI
     */
    generateModeToggleUI() {
        return `
            <div class="report-mode-toggle">
                <div class="toggle-header">
                    <h4>📊 Reporting Mode</h4>
                    <p>Choose between traditional pre-reporting or intelligent final reporting</p>
                </div>
                
                <div class="toggle-buttons">
                    <button class="mode-toggle-btn ${this.currentMode === 'pre-reporting' ? 'active' : ''}" 
                            onclick="window.reportDistinctionManager.switchMode('pre-reporting')">
                        <i class="fas fa-list"></i>
                        <span>Pre-Reporting</span>
                        <small>Traditional data source</small>
                    </button>
                    
                    <button class="mode-toggle-btn ${this.currentMode === 'smart-reporter' ? 'active' : ''}" 
                            onclick="window.reportDistinctionManager.switchMode('smart-reporter')">
                        <i class="fas fa-brain"></i>
                        <span>Smart Reporter</span>
                        <small>Intelligent analysis</small>
                    </button>
                </div>
                
                <div id="report-mode-indicator"></div>
                <div id="report-features"></div>
            </div>
        `;
    }

    /**
     * Get current mode
     */
    getCurrentMode() {
        return this.currentMode;
    }

    /**
     * Get report type configuration
     */
    getReportTypeConfig(type) {
        return type === 'pre-report' ? this.reportTypes.preReport : this.reportTypes.finalReport;
    }

    /**
     * Initialize distinction manager
     */
    initialize() {
        // Set initial mode
        this.updateUIForMode(this.currentMode);
        
        // Set up event listeners for configuration changes
        if (window.dualReportConfiguration) {
            window.dualReportConfiguration.addConfigurationChangeListener(() => {
                this.updateFinalReportButtonState();
            });
        }
        
        console.log('✅ Report Distinction Manager initialized');
    }
}

// Export for use in Final Report Interface
window.ReportDistinctionManager = ReportDistinctionManager;
