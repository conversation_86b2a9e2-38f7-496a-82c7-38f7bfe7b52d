#!/usr/bin/env python3
"""
Diagnose Toggle Loading Issue
Investigates why the UI toggles are not reflecting the saved database values
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    return None

def diagnose_toggle_loading():
    """Diagnose the toggle loading issue"""
    print("🔍 DIAGNOSING TOGGLE LOADING ISSUE")
    print("=" * 60)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Test 1: Check what the backend API returns
        print("\n📊 TEST 1: CHECKING DATABASE STRUCTURE")
        print("-" * 40)
        
        # Check dictionary_sections table
        cursor.execute('SELECT id, section_name FROM dictionary_sections ORDER BY section_name')
        sections = cursor.fetchall()
        print(f"✅ Found {len(sections)} sections:")
        for section in sections:
            print(f"   {section[0]}: {section[1]}")
        
        # Test 2: Check specific items with non-default values
        print("\n📊 TEST 2: CHECKING ITEMS WITH NON-DEFAULT VALUES")
        print("-" * 40)
        
        cursor.execute('''
            SELECT ds.section_name, di.item_name, di.include_new, di.include_increase, 
                   di.include_decrease, di.include_removed, di.include_no_change, di.include_in_report
            FROM dictionary_items di
            JOIN dictionary_sections ds ON di.section_id = ds.id
            WHERE di.include_new = 0 OR di.include_increase = 0 OR di.include_decrease = 0 
               OR di.include_removed = 0 OR di.include_no_change = 0 OR di.include_in_report = 0
            ORDER BY ds.section_name, di.item_name
        ''')
        
        non_default_items = cursor.fetchall()
        print(f"✅ Found {len(non_default_items)} items with non-default values:")
        for item in non_default_items:
            section, name, new, inc, dec, rem, no_change, report = item
            print(f"   {section}.{name}: NEW:{new} INC:{inc} DEC:{dec} REM:{rem} NO_CHANGE:{no_change} REPORT:{report}")
        
        # Test 3: Simulate what the backend API should return
        print("\n📊 TEST 3: SIMULATING BACKEND API RESPONSE")
        print("-" * 40)
        
        # Build dictionary structure like the backend would
        dictionary = {}
        
        # Get all sections
        for section_id, section_name in sections:
            dictionary[section_name] = {
                'items': {}
            }
            
            # Get items for this section
            cursor.execute('''
                SELECT item_name, format, value_format, include_in_report, standardized_name,
                       variations, final_regex, include_new, include_increase, include_decrease,
                       include_removed, include_no_change
                FROM dictionary_items 
                WHERE section_id = ?
                ORDER BY item_name
            ''', (section_id,))
            
            items = cursor.fetchall()
            for item in items:
                item_name, format_val, value_format, include_in_report, standardized_name, variations, final_regex, include_new, include_increase, include_decrease, include_removed, include_no_change = item
                
                # Parse variations JSON
                try:
                    variations_list = json.loads(variations) if variations else []
                except:
                    variations_list = []
                
                dictionary[section_name]['items'][item_name] = {
                    'format': format_val,
                    'value_format': value_format,
                    'include_in_report': bool(include_in_report),
                    'standardized_name': standardized_name,
                    'variations': variations_list,
                    'final_regex': final_regex,
                    'include_new': bool(include_new),
                    'include_increase': bool(include_increase),
                    'include_decrease': bool(include_decrease),
                    'include_removed': bool(include_removed),
                    'include_no_change': bool(include_no_change)
                }
        
        # Test 4: Check specific problematic items
        print("\n📊 TEST 4: CHECKING SPECIFIC PROBLEMATIC ITEMS")
        print("-" * 40)
        
        test_items = ['SSF NO.', 'EMPLOYEE NAME', 'BASIC SALARY', 'DEPARTMENT']
        for test_item in test_items:
            found = False
            for section_name, section_data in dictionary.items():
                if test_item in section_data['items']:
                    item_data = section_data['items'][test_item]
                    print(f"✅ {section_name}.{test_item}:")
                    print(f"   include_new: {item_data['include_new']}")
                    print(f"   include_increase: {item_data['include_increase']}")
                    print(f"   include_decrease: {item_data['include_decrease']}")
                    print(f"   include_removed: {item_data['include_removed']}")
                    print(f"   include_no_change: {item_data['include_no_change']}")
                    print(f"   include_in_report: {item_data['include_in_report']}")
                    found = True
                    break
            
            if not found:
                print(f"❌ {test_item}: Not found in dictionary")
        
        # Test 5: Check if the issue is in the UI loading logic
        print("\n📊 TEST 5: UI LOADING LOGIC ANALYSIS")
        print("-" * 40)
        
        print("✅ The backend API should return the correct toggle states")
        print("✅ Database contains the saved toggle states")
        print("⚠️ POTENTIAL ISSUE: UI might not be reading the values correctly")
        
        # Check if there's a mismatch in boolean conversion
        print("\n🔍 BOOLEAN CONVERSION CHECK:")
        for section_name, section_data in dictionary.items():
            for item_name, item_data in section_data['items'].items():
                if (not item_data['include_new'] or not item_data['include_increase'] or 
                    not item_data['include_decrease'] or not item_data['include_removed'] or 
                    not item_data['include_no_change'] or not item_data['include_in_report']):
                    
                    print(f"   {section_name}.{item_name}:")
                    print(f"     Raw DB values: NEW:{item_data['include_new']} INC:{item_data['include_increase']}")
                    print(f"     Boolean conversion: NEW:{bool(item_data['include_new'])} INC:{bool(item_data['include_increase'])}")
                    break
        
        conn.close()
        
        # Test 6: Generate recommendations
        print("\n💡 RECOMMENDATIONS:")
        print("-" * 40)
        print("1. ✅ Database persistence is working correctly")
        print("2. ✅ Backend API should return correct values")
        print("3. ⚠️ Issue likely in UI rendering logic:")
        print("   - Check if itemData[columnName] is being read correctly")
        print("   - Verify boolean conversion in line 481 of dictionary_manager.js")
        print("   - Ensure the backend API is returning the correct data structure")
        print("4. 🔧 NEXT STEPS:")
        print("   - Add console.log to renderSection function to debug")
        print("   - Verify the backend API response structure")
        print("   - Check if there's a timing issue with data loading")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = diagnose_toggle_loading()
    sys.exit(0 if success else 1)
