#!/usr/bin/env python3
"""
Generate actual Word document report using real payroll data
"""

import sqlite3
import json
from datetime import datetime
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_TABLE_ALIGNMENT

def load_real_payroll_data():
    """Load real payroll data from database"""
    print("📥 LOADING REAL PAYROLL DATA FOR WORD REPORT")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()
        session_id = current_session[0]
        
        print(f"✅ Session: {session_id}")
        
        # Load comparison results for employee-based report
        cursor.execute('''
            SELECT employee_id, employee_name, section_name, item_label, 
                   previous_value, current_value, change_type, priority
            FROM comparison_results 
            WHERE session_id = ? AND priority IN ('HIGH', 'MODERATE')
            ORDER BY employee_name, section_name, item_label
            LIMIT 100
        ''', (session_id,))
        
        results = cursor.fetchall()
        
        # Group by employee
        employees = {}
        for row in results:
            emp_id = row[0]
            emp_name = row[1]
            
            if emp_id not in employees:
                employees[emp_id] = {
                    'name': emp_name,
                    'changes': []
                }
            
            change = {
                'section': row[2],
                'item': row[3],
                'old_value': row[4],
                'new_value': row[5],
                'change_type': row[6],
                'priority': row[7]
            }
            employees[emp_id]['changes'].append(change)
        
        print(f"✅ Loaded data for {len(employees)} employees")
        return session_id, employees
        
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None, None
    finally:
        conn.close()

def create_employee_based_word_report(session_id, employees_data):
    """Create employee-based Word document report"""
    print("\n📄 GENERATING EMPLOYEE-BASED WORD REPORT")
    print("=" * 50)
    
    # Create new document
    doc = Document()
    
    # Add title
    title = doc.add_heading('PAYROLL AUDIT REPORT', 0)
    title.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add subtitle
    subtitle = doc.add_heading('Employee-Based Analysis', level=1)
    subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
    
    # Add metadata
    doc.add_paragraph()
    metadata_para = doc.add_paragraph()
    metadata_para.add_run('Generated By: ').bold = True
    metadata_para.add_run('Senior Payroll Auditor\n')
    metadata_para.add_run('Report Date: ').bold = True
    metadata_para.add_run(f'{datetime.now().strftime("%B %d, %Y")}\n')
    metadata_para.add_run('Session ID: ').bold = True
    metadata_para.add_run(f'{session_id}\n')
    metadata_para.add_run('Report Type: ').bold = True
    metadata_para.add_run('Employee-Based Analysis (HIGH & MODERATE Priority)')
    
    # Add executive summary
    doc.add_heading('EXECUTIVE SUMMARY', level=1)
    
    total_employees = len(employees_data)
    total_changes = sum(len(emp['changes']) for emp in employees_data.values())
    high_priority = sum(len([c for c in emp['changes'] if c['priority'] == 'HIGH']) for emp in employees_data.values())
    moderate_priority = sum(len([c for c in emp['changes'] if c['priority'] == 'MODERATE']) for emp in employees_data.values())
    
    summary_para = doc.add_paragraph()
    summary_para.add_run(f'This report analyzes payroll changes for {total_employees} employees, ')
    summary_para.add_run(f'covering {total_changes} significant changes identified during the audit process. ')
    summary_para.add_run(f'The analysis focuses on HIGH priority ({high_priority} changes) and ')
    summary_para.add_run(f'MODERATE priority ({moderate_priority} changes) items that require attention.')
    
    # Add summary statistics table
    doc.add_paragraph()
    summary_table = doc.add_table(rows=1, cols=2)
    summary_table.style = 'Table Grid'
    summary_table.alignment = WD_TABLE_ALIGNMENT.CENTER
    
    # Header row
    hdr_cells = summary_table.rows[0].cells
    hdr_cells[0].text = 'Metric'
    hdr_cells[1].text = 'Count'
    
    # Add data rows
    metrics = [
        ('Total Employees Analyzed', total_employees),
        ('Total Changes Identified', total_changes),
        ('High Priority Changes', high_priority),
        ('Moderate Priority Changes', moderate_priority)
    ]
    
    for metric, count in metrics:
        row_cells = summary_table.add_row().cells
        row_cells[0].text = metric
        row_cells[1].text = str(count)
    
    # Add detailed findings
    doc.add_heading('DETAILED FINDINGS BY EMPLOYEE', level=1)
    
    # Process each employee
    for emp_id, emp_data in list(employees_data.items())[:10]:  # First 10 employees for demo
        doc.add_heading(f'Employee: {emp_data["name"]} ({emp_id})', level=2)
        
        if emp_data['changes']:
            # Create table for employee changes
            emp_table = doc.add_table(rows=1, cols=5)
            emp_table.style = 'Table Grid'
            
            # Header row
            hdr_cells = emp_table.rows[0].cells
            hdr_cells[0].text = 'Section'
            hdr_cells[1].text = 'Item'
            hdr_cells[2].text = 'Previous Value'
            hdr_cells[3].text = 'Current Value'
            hdr_cells[4].text = 'Priority'
            
            # Add change rows
            for change in emp_data['changes']:
                row_cells = emp_table.add_row().cells
                row_cells[0].text = change['section']
                row_cells[1].text = change['item']
                row_cells[2].text = str(change['old_value'])
                row_cells[3].text = str(change['new_value'])
                row_cells[4].text = change['priority']
            
            doc.add_paragraph()
        else:
            doc.add_paragraph('No significant changes identified for this employee.')
    
    # Add conclusion
    doc.add_heading('CONCLUSION', level=1)
    conclusion_para = doc.add_paragraph()
    conclusion_para.add_run('This employee-based analysis provides a comprehensive view of payroll changes ')
    conclusion_para.add_run('organized by individual employees. The report focuses on HIGH and MODERATE ')
    conclusion_para.add_run('priority changes that require review and verification. Each employee section ')
    conclusion_para.add_run('details the specific changes identified during the audit process.')
    
    # Save document
    filename = f'Payroll_Audit_Report_Employee_Based_{datetime.now().strftime("%Y%m%d_%H%M%S")}.docx'
    doc.save(filename)
    
    print(f"✅ Word report generated: {filename}")
    print(f"📊 Report contains:")
    print(f"   - {total_employees} employees analyzed")
    print(f"   - {total_changes} changes documented")
    print(f"   - {high_priority} high priority items")
    print(f"   - {moderate_priority} moderate priority items")
    
    return filename

def main():
    """Generate employee-based Word report with real data"""
    print("📄 GENERATING EMPLOYEE-BASED WORD REPORT")
    print("=" * 60)
    print("Using real payroll audit data from the system")
    print("=" * 60)
    
    try:
        # Load real data
        session_id, employees_data = load_real_payroll_data()
        
        if not employees_data:
            print("❌ Failed to load real data")
            return False
        
        # Generate Word report
        filename = create_employee_based_word_report(session_id, employees_data)
        
        print(f"\n🎉 SUCCESS!")
        print(f"📄 Employee-based Word report generated: {filename}")
        print(f"📁 Location: Current directory")
        print(f"📊 Format: Microsoft Word (.docx)")
        print(f"🎯 Content: Real payroll audit data organized by employee")
        print(f"\n✅ You can now review the actual Word document!")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating report: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    main()
