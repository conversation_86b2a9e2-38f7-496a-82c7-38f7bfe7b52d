/**
 * PROMOTION AND TRANSFER APPENDIX GENERATOR
 * Implements automatic appendix generation according to specification
 * Creates categorized appendix sections for Ministers vs Staff
 */

class AppendixGenerator {
    constructor() {
        this.appendixRules = this.initializeAppendixRules();
        console.log('📋 Appendix Generator initialized with specification compliance');
    }

    /**
     * Initialize appendix rules from specification
     */
    initializeAppendixRules() {
        return {
            // Appendix sections from specification (exact order and naming)
            sections: {
                promotions: {
                    ministers: {
                        title: 'PROMOTIONS MINISTERS',
                        description: 'Minister promotions (Job title change only)',
                        format: '•{employee_id}: {employee_name} – {department}: {old_title} to {new_title}'
                    },
                    staff: {
                        title: 'PROMOTIONS STAFF',
                        description: 'Staff promotions (Basic salary increase + Job title change)',
                        format: '•{employee_id}: {employee_name} – {department}: {old_title} to {new_title}'
                    }
                },
                transfers: {
                    ministers: {
                        title: 'TRANSFERS MINISTER',
                        description: 'Minister transfers (Department changes)',
                        format: '•{employee_id}: {employee_name} – {department}: {old_dept} TO {new_dept}'
                    },
                    staff: {
                        title: 'TRANSFERS STAFF',
                        description: 'Staff transfers (Department changes)',
                        format: '•{employee_id}: {employee_name} – {department}: From {old_dept} TO {new_dept}'
                    }
                }
            },
            
            // Ordering for appendix sections
            sectionOrder: [
                'PROMOTIONS MINISTERS',
                'PROMOTIONS STAFF',
                'TRANSFERS MINISTER',
                'TRANSFERS STAFF'
            ]
        };
    }

    /**
     * Generate complete appendix from intelligent analysis results
     * @param {Object} intelligentAnalysisResults - Results from BusinessRulesEngine
     * @returns {Array} Formatted appendix sections
     */
    generateAppendix(intelligentAnalysisResults) {
        console.log('📋 Generating appendix sections with automatic categorization...');
        
        const appendixSections = [];
        
        // Process promotions
        const promotionSections = this.generatePromotionSections(intelligentAnalysisResults.promotions);
        appendixSections.push(...promotionSections);
        
        // Process transfers
        const transferSections = this.generateTransferSections(intelligentAnalysisResults.transfers);
        appendixSections.push(...transferSections);
        
        // Sort sections according to specification order
        const orderedSections = this.orderAppendixSections(appendixSections);
        
        console.log(`✅ Generated ${orderedSections.length} appendix sections`);
        return orderedSections;
    }

    /**
     * Generate promotion sections (Ministers and Staff)
     */
    generatePromotionSections(promotions) {
        const sections = [];
        
        // PROMOTIONS MINISTERS
        if (promotions.ministers && promotions.ministers.length > 0) {
            sections.push({
                title: this.appendixRules.sections.promotions.ministers.title,
                description: this.appendixRules.sections.promotions.ministers.description,
                items: promotions.ministers.map(promo => this.formatPromotionItem(promo, 'ministers')),
                count: promotions.ministers.length,
                type: 'promotion',
                category: 'ministers'
            });
        }
        
        // PROMOTIONS STAFF
        if (promotions.staff && promotions.staff.length > 0) {
            sections.push({
                title: this.appendixRules.sections.promotions.staff.title,
                description: this.appendixRules.sections.promotions.staff.description,
                items: promotions.staff.map(promo => this.formatPromotionItem(promo, 'staff')),
                count: promotions.staff.length,
                type: 'promotion',
                category: 'staff'
            });
        }
        
        return sections;
    }

    /**
     * Generate transfer sections (Ministers and Staff)
     */
    generateTransferSections(transfers) {
        const sections = [];
        
        // TRANSFERS MINISTER
        if (transfers.ministers && transfers.ministers.length > 0) {
            sections.push({
                title: this.appendixRules.sections.transfers.ministers.title,
                description: this.appendixRules.sections.transfers.ministers.description,
                items: transfers.ministers.map(transfer => this.formatTransferItem(transfer, 'ministers')),
                count: transfers.ministers.length,
                type: 'transfer',
                category: 'ministers'
            });
        }
        
        // TRANSFERS STAFF
        if (transfers.staff && transfers.staff.length > 0) {
            sections.push({
                title: this.appendixRules.sections.transfers.staff.title,
                description: this.appendixRules.sections.transfers.staff.description,
                items: transfers.staff.map(transfer => this.formatTransferItem(transfer, 'staff')),
                count: transfers.staff.length,
                type: 'transfer',
                category: 'staff'
            });
        }
        
        return sections;
    }

    /**
     * Format promotion item according to specification
     */
    formatPromotionItem(promotion, category) {
        const template = this.appendixRules.sections.promotions[category].format;
        
        return template
            .replace('{employee_id}', promotion.employeeId)
            .replace('{employee_name}', promotion.employeeName)
            .replace('{department}', promotion.department)
            .replace('{old_title}', promotion.oldTitle)
            .replace('{new_title}', promotion.newTitle);
    }

    /**
     * Format transfer item according to specification
     */
    formatTransferItem(transfer, category) {
        const template = this.appendixRules.sections.transfers[category].format;
        
        return template
            .replace('{employee_id}', transfer.employeeId)
            .replace('{employee_name}', transfer.employeeName)
            .replace('{department}', transfer.department)
            .replace('{old_dept}', transfer.oldDepartment)
            .replace('{new_dept}', transfer.newDepartment);
    }

    /**
     * Order appendix sections according to specification
     */
    orderAppendixSections(sections) {
        const orderedSections = [];
        
        // Add sections in the specified order
        this.appendixRules.sectionOrder.forEach(sectionTitle => {
            const section = sections.find(s => s.title === sectionTitle);
            if (section) {
                orderedSections.push(section);
            }
        });
        
        return orderedSections;
    }

    /**
     * Generate appendix summary statistics
     */
    generateAppendixSummary(appendixSections) {
        const summary = {
            totalSections: appendixSections.length,
            totalItems: 0,
            breakdown: {
                promotions: {
                    ministers: 0,
                    staff: 0
                },
                transfers: {
                    ministers: 0,
                    staff: 0
                }
            }
        };
        
        appendixSections.forEach(section => {
            summary.totalItems += section.count;
            
            if (section.type === 'promotion') {
                summary.breakdown.promotions[section.category] = section.count;
            } else if (section.type === 'transfer') {
                summary.breakdown.transfers[section.category] = section.count;
            }
        });
        
        return summary;
    }

    /**
     * Validate appendix data
     */
    validateAppendixData(appendixSections) {
        const errors = [];
        
        appendixSections.forEach((section, index) => {
            if (!section.title) {
                errors.push(`Section ${index + 1}: Missing title`);
            }
            
            if (!section.items || section.items.length === 0) {
                errors.push(`Section ${index + 1}: No items found`);
            }
            
            if (!section.type || !['promotion', 'transfer'].includes(section.type)) {
                errors.push(`Section ${index + 1}: Invalid type`);
            }
            
            if (!section.category || !['ministers', 'staff'].includes(section.category)) {
                errors.push(`Section ${index + 1}: Invalid category`);
            }
        });
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Generate appendix preview for UI display
     */
    generateAppendixPreview(appendixSections) {
        return appendixSections.map(section => ({
            title: section.title,
            count: section.count,
            type: section.type,
            category: section.category,
            preview: section.items.slice(0, 3), // Show first 3 items
            hasMore: section.items.length > 3
        }));
    }

    /**
     * Export appendix data for Word document generation
     */
    exportForWordDocument(appendixSections) {
        return appendixSections.map(section => ({
            title: section.title,
            items: section.items,
            formatting: {
                titleStyle: 'heading',
                itemStyle: 'body',
                indent: 1,
                spacing: { before: 12, after: 6 }
            }
        }));
    }

    /**
     * Get appendix rules for external use
     */
    getAppendixRules() {
        return this.appendixRules;
    }
}

// Export for use in Final Report Interface
window.AppendixGenerator = AppendixGenerator;
