#!/usr/bin/env python3
"""
Generate Word report in the reports folder for easy access
"""

import sqlite3
import os
from datetime import datetime
from docx import Document
from docx.shared import Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH

def create_reports_folder():
    """Create reports folder if it doesn't exist"""
    reports_folder = "./reports/Payroll_Audit_Reports"
    if not os.path.exists(reports_folder):
        os.makedirs(reports_folder)
    return reports_folder

def generate_word_report_in_folder():
    """Generate Word report in the reports folder"""
    print("📄 GENERATING WORD REPORT IN REPORTS FOLDER")
    print("=" * 50)
    
    # Create reports folder
    reports_folder = create_reports_folder()
    print(f"✅ Reports folder: {reports_folder}")
    
    try:
        # Load real data
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Get current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()
        session_id = current_session[0]
        
        # Load sample data
        cursor.execute('''
            SELECT employee_id, employee_name, section_name, item_label, 
                   previous_value, current_value, change_type, priority
            FROM comparison_results 
            WHERE session_id = ? AND priority IN ('HIGH', 'MODERATE')
            ORDER BY employee_name, section_name
            LIMIT 50
        ''', (session_id,))
        
        results = cursor.fetchall()
        conn.close()
        
        print(f"✅ Loaded {len(results)} changes")
        
        # Create Word document
        doc = Document()
        
        # Title
        title = doc.add_heading('PAYROLL AUDIT REPORT', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Subtitle
        subtitle = doc.add_heading('Employee-Based Analysis Report', level=1)
        subtitle.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # Metadata
        doc.add_paragraph()
        metadata = doc.add_paragraph()
        metadata.add_run('Generated By: ').bold = True
        metadata.add_run('Payroll Auditor\n')
        metadata.add_run('Report Date: ').bold = True
        metadata.add_run(f'{datetime.now().strftime("%B %d, %Y at %I:%M %p")}\n')
        metadata.add_run('Session ID: ').bold = True
        metadata.add_run(f'{session_id}\n')
        metadata.add_run('Total Changes Analyzed: ').bold = True
        metadata.add_run(f'{len(results)} high/moderate priority changes')
        
        # Executive Summary
        doc.add_heading('EXECUTIVE SUMMARY', level=1)
        
        employees = set(row[1] for row in results)
        sections = set(row[2] for row in results)
        high_priority = len([r for r in results if r[7] == 'HIGH'])
        moderate_priority = len([r for r in results if r[7] == 'MODERATE'])
        
        summary = doc.add_paragraph()
        summary.add_run(f'This report analyzes {len(results)} significant payroll changes ')
        summary.add_run(f'affecting {len(employees)} employees across {len(sections)} different sections. ')
        summary.add_run(f'The analysis includes {high_priority} HIGH priority changes and ')
        summary.add_run(f'{moderate_priority} MODERATE priority changes that require attention.')
        
        # Sample Changes Table
        doc.add_heading('SAMPLE PAYROLL CHANGES', level=1)
        
        # Create table
        table = doc.add_table(rows=1, cols=6)
        table.style = 'Table Grid'
        
        # Header row
        hdr_cells = table.rows[0].cells
        hdr_cells[0].text = 'Employee'
        hdr_cells[1].text = 'Section'
        hdr_cells[2].text = 'Item'
        hdr_cells[3].text = 'Previous Value'
        hdr_cells[4].text = 'Current Value'
        hdr_cells[5].text = 'Priority'
        
        # Add data rows (first 20 for readability)
        for row in results[:20]:
            row_cells = table.add_row().cells
            row_cells[0].text = row[1]  # employee_name
            row_cells[1].text = row[2]  # section_name
            row_cells[2].text = row[3]  # item_label
            row_cells[3].text = str(row[4])  # previous_value
            row_cells[4].text = str(row[5])  # current_value
            row_cells[5].text = row[7]  # priority
        
        # Conclusion
        doc.add_heading('CONCLUSION', level=1)
        conclusion = doc.add_paragraph()
        conclusion.add_run('This employee-based analysis provides a comprehensive overview of ')
        conclusion.add_run('significant payroll changes identified during the audit process. ')
        conclusion.add_run('All HIGH and MODERATE priority changes have been documented for review.')
        
        # Save in reports folder
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"Employee_Based_Payroll_Report_{timestamp}.docx"
        filepath = os.path.join(reports_folder, filename)
        
        doc.save(filepath)
        
        print(f"✅ Report saved successfully!")
        print(f"📁 Location: {filepath}")
        print(f"📄 Filename: {filename}")
        print(f"📊 Contains: {len(results)} changes from {len(employees)} employees")
        
        # Also save in current directory for easy access
        current_dir_file = f"PAYROLL_REPORT_{timestamp}.docx"
        doc.save(current_dir_file)
        print(f"📄 Also saved in current directory: {current_dir_file}")
        
        return filepath, current_dir_file
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return None, None

def main():
    """Main function"""
    print("🎯 CREATING EMPLOYEE-BASED PAYROLL AUDIT REPORT")
    print("=" * 60)
    
    reports_path, current_path = generate_word_report_in_folder()
    
    if reports_path and current_path:
        print(f"\n🎉 SUCCESS! Word reports generated:")
        print(f"📁 Reports folder: {reports_path}")
        print(f"📄 Current directory: {current_path}")
        print(f"\n✅ You can find the report in either location!")
        print(f"💡 Try opening: {current_path}")
        
        # Try to open the file
        try:
            import subprocess
            subprocess.run(['start', current_path], shell=True, check=False)
            print(f"🚀 Attempting to open the report...")
        except:
            print(f"📝 Please manually open: {current_path}")
    else:
        print(f"❌ Failed to generate report")

if __name__ == "__main__":
    main()
