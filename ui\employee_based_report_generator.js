/**
 * EMPLOYEE-BASED REPORT GENERATOR
 * Implements exact format from specification screenshot
 * Generates Word documents with strict adherence to provided template
 */

class EmployeeBasedReportGenerator {
    constructor() {
        this.templateFormat = this.initializeTemplateFormat();
        console.log('👥 Employee-Based Report Generator initialized');
    }

    /**
     * Initialize template format according to specification screenshot
     */
    initializeTemplateFormat() {
        return {
            // Font specifications from FINAL REPORT.txt
            fonts: {
                body: 'Cambria (Body)',
                bodySize: 14,
                heading: '<PERSON><PERSON><PERSON> (Headings)', 
                headingSize: 26
            },
            
            // Report structure from screenshot
            structure: {
                title: 'PAYROLL AUDIT REPORT: {MONTH} {YEAR}',
                sections: [
                    'reportInformation',
                    'executiveSummary', 
                    'findingsAndObservations',
                    'newEmployees',
                    'removedEmployees',
                    'appendices'
                ]
            },
            
            // Table formats from screenshot
            tables: {
                reportInformation: {
                    headers: ['Report Information', 'Executive Summary'],
                    leftColumn: ['Report Period', 'Generated at', 'Generated By', 'Designation'],
                    rightColumn: ['Significant Changes Detected', 'HIGH Priority Changes', 'MODERATE Priority Changes', 'LOW Priority Changes']
                }
            },
            
            // Employee finding format from screenshot
            employeeFormat: {
                header: '{employee_id}: {employee_name} – {department}',
                changeNumbering: true,
                narrationStyle: 'specification_compliant'
            }
        };
    }

    /**
     * Generate Employee-Based Report according to specification
     * @param {Object} intelligentAnalysisResults - Results from BusinessRulesEngine
     * @param {Object} reportConfig - Report configuration (Generated By, Designation)
     * @returns {Object} Generated report data for Word document creation
     */
    generateEmployeeBasedReport(intelligentAnalysisResults, reportConfig) {
        console.log('👥 Generating Employee-Based Report with specification compliance...');
        
        const reportData = {
            metadata: this.generateReportMetadata(intelligentAnalysisResults, reportConfig),
            reportInformation: this.generateReportInformationTable(intelligentAnalysisResults, reportConfig),
            executiveSummary: this.generateExecutiveSummaryTable(intelligentAnalysisResults),
            findingsAndObservations: this.generateFindingsSection(intelligentAnalysisResults),
            newEmployees: this.generateNewEmployeesSection(intelligentAnalysisResults),
            removedEmployees: this.generateRemovedEmployeesSection(intelligentAnalysisResults),
            appendices: this.generateAppendices(intelligentAnalysisResults)
        };
        
        console.log('✅ Employee-Based Report data generated');
        return reportData;
    }

    /**
     * Generate report metadata
     */
    generateReportMetadata(results, config) {
        const currentDate = new Date();
        const month = currentDate.toLocaleString('default', { month: 'long' }).toUpperCase();
        const year = currentDate.getFullYear();
        
        return {
            title: `PAYROLL AUDIT REPORT: ${month} ${year}`,
            reportPeriod: `${month} ${year}`,
            generatedAt: currentDate.toLocaleString('en-US', {
                year: 'numeric',
                month: '2-digit', 
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                hour12: false
            }),
            generatedBy: config.generatedBy || 'System Generated',
            designation: config.designation || 'Audit Officer',
            month,
            year
        };
    }

    /**
     * Generate Report Information Table (Left side of screenshot table)
     */
    generateReportInformationTable(results, config) {
        const metadata = this.generateReportMetadata(results, config);
        
        return {
            'Report Period': metadata.reportPeriod,
            'Generated at': metadata.generatedAt,
            'Generated By': metadata.generatedBy,
            'Designation': metadata.designation
        };
    }

    /**
     * Generate Executive Summary Table (Right side of screenshot table)
     */
    generateExecutiveSummaryTable(results) {
        const summary = results.executiveSummary;
        
        return {
            'Significant Changes Detected': summary.significantChangesDetected || 0,
            'HIGH Priority Changes': summary.highPriorityChanges || 0,
            'MODERATE Priority Changes': summary.moderatePriorityChanges || 0,
            'LOW Priority Changes': summary.lowPriorityChanges || 0
        };
    }

    /**
     * Generate Findings and Observations Section (Main content from screenshot)
     */
    generateFindingsSection(results) {
        const findings = [];
        
        // Process employee-based data according to specification format
        results.employeeBasedData.forEach(employee => {
            if (employee.changes && employee.changes.length > 0) {
                const employeeSection = {
                    header: `${employee.employeeId}: ${employee.employeeName} – ${employee.department}`,
                    changes: employee.changes
                        .filter(change => change.includeInReport)
                        .map((change, index) => ({
                            number: index + 1,
                            narration: change.narration,
                            changeType: change.changeType,
                            priority: change.priority
                        }))
                };
                
                if (employeeSection.changes.length > 0) {
                    findings.push(employeeSection);
                }
            }
        });
        
        return findings;
    }

    /**
     * Generate New Employees Section
     */
    generateNewEmployeesSection(results) {
        if (!results.newEmployees || results.newEmployees.length === 0) {
            return null;
        }
        
        return {
            title: 'NEW EMPLOYEES',
            description: 'The following Employees were added to ' + results.metadata.reportMonth + ' Payroll:',
            employees: results.newEmployees.map(emp => ({
                employeeId: emp.employee_id,
                employeeName: emp.employee_name,
                department: emp.department,
                formatted: `${emp.employee_id}: ${emp.employee_name} – ${emp.department}`
            }))
        };
    }

    /**
     * Generate Removed Employees Section  
     */
    generateRemovedEmployeesSection(results) {
        if (!results.removedEmployees || results.removedEmployees.length === 0) {
            return null;
        }
        
        return {
            title: 'REMOVED EMPLOYEES',
            description: 'The following Employees were removed from ' + results.metadata.reportMonth + ' Payroll:',
            employees: results.removedEmployees.map(emp => ({
                employeeId: emp.employee_id,
                employeeName: emp.employee_name,
                department: emp.department,
                formatted: `${emp.employee_id}: ${emp.employee_name} – ${emp.department}`
            }))
        };
    }

    /**
     * Generate Appendices (Promotions and Transfers)
     */
    generateAppendices(results) {
        const appendices = [];
        
        // Promotions Ministers
        if (results.promotions?.ministers?.length > 0) {
            appendices.push({
                title: 'PROMOTIONS MINISTERS',
                items: results.promotions.ministers.map(promo => promo.formattedText)
            });
        }
        
        // Promotions Staff
        if (results.promotions?.staff?.length > 0) {
            appendices.push({
                title: 'PROMOTIONS STAFF', 
                items: results.promotions.staff.map(promo => promo.formattedText)
            });
        }
        
        // Transfers Ministers
        if (results.transfers?.ministers?.length > 0) {
            appendices.push({
                title: 'TRANSFERS MINISTER',
                items: results.transfers.ministers.map(transfer => transfer.formattedText)
            });
        }
        
        // Transfers Staff
        if (results.transfers?.staff?.length > 0) {
            appendices.push({
                title: 'TRANSFERS STAFF',
                items: results.transfers.staff.map(transfer => transfer.formattedText)
            });
        }
        
        return appendices;
    }

    /**
     * Validate report data before generation
     */
    validateReportData(reportData) {
        const errors = [];
        
        if (!reportData.metadata?.title) {
            errors.push('Missing report title');
        }
        
        if (!reportData.reportInformation?.['Generated By']) {
            errors.push('Missing Generated By information');
        }
        
        if (!reportData.reportInformation?.['Designation']) {
            errors.push('Missing Designation information');
        }
        
        if (!reportData.findingsAndObservations || reportData.findingsAndObservations.length === 0) {
            errors.push('No findings to report');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * Get template format for Word document generation
     */
    getTemplateFormat() {
        return this.templateFormat;
    }
}

// Export for use in Final Report Interface
window.EmployeeBasedReportGenerator = EmployeeBasedReportGenerator;
