/**
 * THE PAYROLL AUDITOR - Payroll Comparison System
 * Modular Renderer Entry Point
 * 
 * This file serves as the main entry point for the renderer process,
 * importing and initializing all the modular components of the application.
 */

// Initialize global app events system
window.appEvents = {
  events: {},
  
  on(event, callback) {
    if (!this.events[event]) this.events[event] = [];
    this.events[event].push(callback);
  },
  
  emit(event, data) {
    if (this.events[event]) {
      this.events[event].forEach(callback => callback(data));
    }
  },
  
  off(event, callback) {
    if (this.events[event]) {
      this.events[event] = this.events[event].filter(cb => cb !== callback);
    }
  }
};

// Global variables
window.auditCurrentPdfPath = null;
window.auditPreviousPdfPath = null;
window.enhancedProcessActive = false;
window.processStartTime = null;

/**
 * Initialize the application when the DOM is fully loaded
 */
document.addEventListener('DOMContentLoaded', function() {
  console.log('🚀 THE PAYROLL AUDITOR - Application Initializing');
  
  // Import modules (these will register their functions on window)
  importModules().then(() => {
    // Set up main UI event listeners
    setupUIEventListeners();
    
    // Initialize tabs
    loadTabContent('payroll-audit');
    
    // Initialize dictionary manager if available
    if (typeof initializeDataBuilderDictionaryManager === 'function') {
      initializeDataBuilderDictionaryManager();
    }
    
    // Initialize reports
    if (typeof loadSavedReports === 'function') {
      loadSavedReports();
    }
    
    console.log('✅ THE PAYROLL AUDITOR - Application Initialized');
  }).catch(error => {
    console.error('Error initializing application:', error);
  });
});

/**
 * Import and initialize all modules
 */
async function importModules() {
  console.log('📦 Loading application modules...');
  
  const modules = [
    '/ui/payroll_audit_core.js',
    '/ui/progress_tracker.js',
    '/ui/phase_manager.js',
    '/ui/report_generator.js',
    '/ui/pre_reporting.js',
    '/ui/content_switching_manager.js',
    '/ui/dictionary_manager.js',
    '/ui/stats_panel_updater.js'
  ];
  
  // Dynamic import for each module
  for (const module of modules) {
    try {
      // Create script element
      const script = document.createElement('script');
      script.src = module;
      script.async = false; // Keep execution order
      
      // Wait for script to load
      await new Promise((resolve, reject) => {
        script.onload = resolve;
        script.onerror = () => reject(new Error(`Failed to load module: ${module}`));
        document.head.appendChild(script);
      });
      
      console.log(`✅ Loaded module: ${module}`);
    } catch (error) {
      console.warn(`⚠️ Could not load module (non-critical): ${module}`, error);
    }
  }
}

/**
 * Set up main UI event listeners
 */
function setupUIEventListeners() {
  // Tab navigation
  const tabLinks = document.querySelectorAll('.tab-link');
  tabLinks.forEach(link => {
    link.addEventListener('click', function() {
      const tabName = this.getAttribute('data-tab');
      if (tabName) {
        switchToTab(tabName);
      }
    });
  });
  
  // Payroll audit file selection
  const currentFileButton = document.getElementById('select-current-file');
  const previousFileButton = document.getElementById('select-previous-file');
  
  if (currentFileButton) {
    currentFileButton.addEventListener('click', () => selectAuditFile('current'));
  }
  
  if (previousFileButton) {
    previousFileButton.addEventListener('click', () => selectAuditFile('previous'));
  }
  
  // Audit process start/stop buttons
  const startAuditButton = document.getElementById('start-audit-button');
  const stopAuditButton = document.getElementById('stop-audit-button');
  
  if (startAuditButton) {
    startAuditButton.addEventListener('click', startPayrollAuditProcess);
  }
  
  if (stopAuditButton) {
    stopAuditButton.addEventListener('click', stopPayrollAuditProcess);
  }
  
  // Signature form elements removed - configuration now in Final Report Interface
  
  // PDF sorter file selection
  const selectPdfButton = document.getElementById('select-pdf-button');
  if (selectPdfButton) {
    selectPdfButton.addEventListener('click', function() {
      if (typeof handlePdfFileSelection === 'function') {
        handlePdfFileSelection();
      }
    });
  }
  
  // Dictionary manager controls
  const refreshDictionaryButton = document.getElementById('refresh-dictionary');
  if (refreshDictionaryButton) {
    refreshDictionaryButton.addEventListener('click', function() {
      if (typeof refreshDataBuilderDictionary === 'function') {
        refreshDataBuilderDictionary();
      }
    });
  }
  
  console.log('✅ UI event listeners initialized');
}

/**
 * Switch to a specific tab
 */
function switchToTab(tabName) {
  // Hide all tab contents
  const tabContents = document.querySelectorAll('.tab-content');
  tabContents.forEach(tab => {
    tab.classList.add('hidden');
  });
  
  // Show selected tab content
  const selectedTab = document.getElementById(`${tabName}-tab`);
  if (selectedTab) {
    selectedTab.classList.remove('hidden');
  }
  
  // Update active tab link
  const tabLinks = document.querySelectorAll('.tab-link');
  tabLinks.forEach(link => {
    link.classList.remove('active');
    if (link.getAttribute('data-tab') === tabName) {
      link.classList.add('active');
    }
  });
  
  // Load tab-specific content
  loadTabContent(tabName);
}

/**
 * Load tab-specific content
 */
function loadTabContent(tabName) {
  console.log(`📂 Loading content for tab: ${tabName}`);
  
  switch (tabName) {
    case 'payroll-audit':
      // Check audit button state based on file selection
      if (typeof checkAuditButtonState === 'function') {
        checkAuditButtonState();
      }
      break;
      
    case 'dictionary-manager':
      // Initialize dictionary manager
      if (typeof initializeDataBuilderDictionaryManager === 'function') {
        initializeDataBuilderDictionaryManager();
      }
      if (typeof refreshDataBuilderDictionary === 'function') {
        refreshDataBuilderDictionary();
      }
      break;
      
    case 'report-manager':
      // Load saved reports
      if (typeof loadSavedReports === 'function') {
        loadSavedReports();
      }
      break;
      
    case 'pdf-sorter':
      // Initialize PDF sorter
      if (typeof initializePdfSorterTab === 'function') {
        initializePdfSorterTab();
      }
      break;
      
    case 'data-builder':
      // Initialize data builder
      if (typeof initializeDataBuilderTab === 'function') {
        initializeDataBuilderTab();
      }
      break;
      
    case 'bank-adviser':
      // Initialize bank adviser
      if (typeof initializeBankAdviserTab === 'function') {
        initializeBankAdviserTab();
      }
      break;
  }
}

// Make functions globally accessible
window.setupUIEventListeners = setupUIEventListeners;
window.switchToTab = switchToTab;
window.loadTabContent = loadTabContent;

console.log('✅ Renderer entry point initialized');
