/**
 * ENHANCED Business Rules Engine for Final Report Interface
 * Implements ALL requirements from FINAL REPORT.txt specification
 * Centralizes intelligent analysis, promotion/transfer detection, and report generation logic
 */

class BusinessRulesEngine {
    constructor() {
        this.rules = {
            // Core business rules from specification
            changeTypes: this.initializeChangeTypeRules(),
            reportingRules: this.initializeReportingRules(),
            loanRules: this.initializeLoanRules(),
            promotionRules: this.initializePromotionRules(),
            transferRules: this.initializeTransferRules(),
            employeeRules: this.initializeEmployeeRules(),

            // Legacy rules (enhanced)
            priority: this.initializePriorityRules(),
            bulkCategorization: this.initializeBulkCategorizationRules(),
            formatting: this.initializeFormattingRules()
        };

        // Current month context for reporting
        this.currentMonth = new Date().toLocaleString('default', { month: 'long', year: 'numeric' });

        console.log('🎯 ENHANCED Business Rules Engine initialized with specification compliance');
    }

    // CHANGE TYPE RULES - Core specification from FINAL REPORT.txt
    initializeChangeTypeRules() {
        return {
            // Change type definitions from specification
            INCREASED: {
                condition: (prev, curr) => parseFloat(curr) > parseFloat(prev),
                narration: (item, prev, curr, month) =>
                    `${item} increased from ${this.formatCurrency(prev)} to ${this.formatCurrency(curr)} in ${month} (increase of ${this.formatCurrency(parseFloat(curr) - parseFloat(prev))})`
            },
            DECREASED: {
                condition: (prev, curr) => parseFloat(curr) < parseFloat(prev),
                narration: (item, prev, curr, month) =>
                    `${item} decreased from ${this.formatCurrency(prev)} to ${this.formatCurrency(curr)} in ${month} (decrease of ${this.formatCurrency(parseFloat(prev) - parseFloat(curr))})`
            },
            NEW: {
                condition: (prev, curr) => (!prev || parseFloat(prev) === 0) && parseFloat(curr) > 0,
                narration: (item, prev, curr, month) =>
                    `${item} of ${this.formatCurrency(curr)} was added to Payslip for ${month}`
            },
            REMOVED: {
                condition: (prev, curr) => parseFloat(prev) > 0 && (!curr || parseFloat(curr) === 0),
                narration: (item, prev, curr, month) =>
                    `${item} of ${this.formatCurrency(prev)} was removed from Payslip for ${month}`
            },
            NO_CHANGE: {
                condition: (prev, curr) => parseFloat(prev) === parseFloat(curr),
                narration: () => null // No changes are not to appear in report
            }
        };
    }

    // REPORTING RULES - From specification
    initializeReportingRules() {
        return {
            employeeBased: {
                // Changes found are reported based on individual employees
                groupBy: 'employee',
                format: 'employee_first',
                numberedChanges: true,
                focusCurrentMonth: true,
                includeMultipleChangeTypes: true
            },
            itemBased: {
                // Item name and narration comes first, followed by affected employees
                groupBy: 'item',
                format: 'item_first',
                employeeListFormat: '•{employee_id}: {employee_name} – {department}'
            },
            departmentChange: {
                template: 'Department changed from {prev_dept} to {curr_dept} in {month}'
            }
        };
    }

    // LOAN REPORTING RULES - Specific from specification
    initializeLoanRules() {
        return {
            // Only report these specific loan changes
            reportableChanges: {
                balanceIncrease: {
                    condition: (change) => change.item_name?.toLowerCase().includes('balance') &&
                                         change.item_name?.toLowerCase().includes('b/f'),
                    template: '{loan_name} Balance increased from {prev_value} to {curr_value} in {month}'
                },
                currentDeductionIncrease: {
                    condition: (change) => change.item_name?.toLowerCase().includes('current deduction'),
                    template: '{loan_name} Current Deduction increased from {prev_value} to {curr_value}'
                }
            },
            loanKeywords: ['loan', 'advance', 'credit', 'deduction', 'balance b/f']
        };
    }

    // PROMOTION RULES - Exact specification from FINAL REPORT.txt
    initializePromotionRules() {
        return {
            staff: {
                // STAFF PROMOTION = Basic salary increase + Job Title change
                condition: (changes) => this.hasBasicSalaryIncrease(changes) && this.hasJobTitleChange(changes),
                template: '{employee_id}: {employee_name} – {department}: {old_title} to {new_title}'
            },
            minister: {
                // MINISTER PROMOTION = Job title change only for persons who have Ministers in their Department value
                condition: (changes) => this.hasJobTitleChange(changes) && this.isMinisterDepartment(changes),
                template: '{employee_id}: {employee_name} – {department}: {old_title} to {new_title}'
            },
            appendixSections: {
                'PROMOTIONS MINISTERS': 'minister',
                'PROMOTIONS STAFF': 'staff'
            }
        };
    }

    // TRANSFER RULES - Exact specification from FINAL REPORT.txt
    initializeTransferRules() {
        return {
            staff: {
                // STAFF TRANSFER = Change in Department
                condition: (changes) => this.hasDepartmentChange(changes) && !this.isMinisterDepartment(changes),
                template: '{employee_id}: {employee_name} – {department}: From {old_dept} TO {new_dept}'
            },
            minister: {
                // MINISTER TRANSFER = Change in Department for Ministers
                condition: (changes) => this.hasDepartmentChange(changes) && this.isMinisterDepartment(changes),
                template: '{employee_id}: {employee_name} – {department}: {old_dept} TO {new_dept}'
            },
            appendixSections: {
                'TRANSFERS MINISTER': 'minister',
                'TRANSFERS STAFF': 'staff'
            }
        };
    }

    // EMPLOYEE RULES - New/Removed employee detection
    initializeEmployeeRules() {
        return {
            newEmployee: {
                // Employee No. appearing in current month but not in Previous month
                condition: (currentData, previousData) =>
                    !previousData.find(p => p.employee_id === currentData.employee_id || p.employee_name === currentData.employee_name)
            },
            removedEmployee: {
                // Employee Name or Employee No. removed from current month but was present in previous
                condition: (previousData, currentData) =>
                    !currentData.find(c => c.employee_id === previousData.employee_id || c.employee_name === previousData.employee_name)
            }
        };
    }

    // PRIORITY CLASSIFICATION RULES (Enhanced)
    initializePriorityRules() {
        return {
            HIGH: [
                'Personal Details', 'Earnings', 'Basic Salary', 'Deductions', 'Bank Details'
            ],
            MODERATE: [
                'Loans', 'Employee Bank Details', 'Allowances'
            ],
            LOW: [
                'Employer Contributions', 'Other Benefits', 'Miscellaneous'
            ]
        };
    }

    // BULK CATEGORIZATION RULES (From specification)
    initializeBulkCategorizationRules() {
        return {
            individual: { maxChanges: 1, description: 'Individual Anomaly' },
            small: { minChanges: 2, maxChanges: 10, description: 'Small Bulk Change' },
            medium: { minChanges: 11, maxChanges: 50, description: 'Medium Bulk Change' },
            large: { minChanges: 51, maxChanges: Infinity, description: 'Large Bulk Change' }
        };
    }

    // FORMATTING RULES - From specification
    initializeFormattingRules() {
        return {
            fonts: {
                body: 'Cambria (Body) 14',
                heading: 'Calibri (Headings) 26'
            },
            currency: 'GHS',
            dateFormat: 'MMMM YYYY',
            reportTitle: 'PAYROLL AUDIT REPORT: {MONTH} {YEAR}'
        };
    }

    // ===== MAIN INTELLIGENT ANALYSIS METHODS =====

    /**
     * ENHANCED: Process payroll changes according to ALL specification requirements
     * @param {Array} changes - Array of payroll changes
     * @param {Array} previousData - Previous month data for comparison
     * @param {Object} config - Processing configuration
     * @returns {Object} Complete intelligent analysis results
     */
    processChanges(changes, previousData = [], config = {}) {
        console.log(`🧠 INTELLIGENT ANALYSIS: Processing ${changes.length} changes with full specification compliance...`);

        const results = {
            // Core change analysis
            analyzedChanges: this.analyzeChangesWithNarration(changes),

            // Employee-based analysis
            employeeBasedData: this.generateEmployeeBasedData(changes),

            // Item-based analysis
            itemBasedData: this.generateItemBasedData(changes),

            // Specialized detection
            promotions: this.detectPromotionsAdvanced(changes),
            transfers: this.detectTransfersAdvanced(changes),
            newEmployees: this.detectNewEmployees(changes, previousData),
            removedEmployees: this.detectRemovedEmployees(changes, previousData),

            // Loan analysis
            loanChanges: this.analyzeLoanChanges(changes),

            // Executive summary data
            executiveSummary: {},

            // Report metadata
            metadata: {
                reportMonth: this.currentMonth,
                generatedAt: new Date().toISOString(),
                totalChanges: changes.length,
                processingConfig: config
            }
        };

        // Generate executive summary
        results.executiveSummary = this.generateExecutiveSummary(results);

        console.log('✅ INTELLIGENT ANALYSIS complete:', results.executiveSummary);
        return results;
    }

    /**
     * Analyze changes with proper narration according to specification
     */
    analyzeChangesWithNarration(changes) {
        return changes.map(change => {
            const changeType = this.determineChangeType(change);
            const narration = this.generateNarration(change, changeType);
            const priority = this.determinePriority(change.section_name);

            return {
                ...change,
                changeType,
                narration,
                priority,
                includeInReport: changeType !== 'NO_CHANGE' && narration !== null,
                isLoanChange: this.isLoanChange(change)
            };
        });
    }

    /**
     * Determine change type according to specification rules
     */
    determineChangeType(change) {
        const prevValue = change.previous_value || '0';
        const currValue = change.current_value || '0';

        for (const [type, rule] of Object.entries(this.rules.changeTypes)) {
            if (rule.condition(prevValue, currValue)) {
                return type;
            }
        }
        return 'NO_CHANGE';
    }

    /**
     * Generate narration according to specification format
     */
    generateNarration(change, changeType) {
        const rule = this.rules.changeTypes[changeType];
        if (!rule || !rule.narration) return null;

        // Handle department changes specially
        if (this.isDepartmentChange(change)) {
            return this.rules.reportingRules.departmentChange.template
                .replace('{prev_dept}', change.previous_value)
                .replace('{curr_dept}', change.current_value)
                .replace('{month}', this.currentMonth);
        }

        return rule.narration(
            change.item_name,
            change.previous_value,
            change.current_value,
            this.currentMonth
        );
    }

    /**
     * Generate Employee-Based Report Data (Specification Format)
     */
    generateEmployeeBasedData(changes) {
        const employeeGroups = this.groupChangesByEmployee(changes);
        const employeeData = [];

        Object.entries(employeeGroups).forEach(([employeeId, empChanges]) => {
            const analyzedChanges = empChanges
                .map(change => this.analyzeChangeWithNarration(change))
                .filter(change => change.includeInReport);

            if (analyzedChanges.length > 0) {
                employeeData.push({
                    employeeId: empChanges[0].employee_id,
                    employeeName: empChanges[0].employee_name,
                    department: empChanges[0].department || empChanges[0].section_name,
                    changes: analyzedChanges,
                    changeCount: analyzedChanges.length,
                    hasPromotion: this.hasPromotionChanges(empChanges),
                    hasTransfer: this.hasTransferChanges(empChanges)
                });
            }
        });

        return employeeData.sort((a, b) => a.employeeName.localeCompare(b.employeeName));
    }

    /**
     * Generate Item-Based Report Data (Specification Format)
     */
    generateItemBasedData(changes) {
        const itemGroups = this.groupChangesByItem(changes);
        const itemData = [];

        Object.entries(itemGroups).forEach(([itemKey, itemChanges]) => {
            const analyzedChanges = itemChanges
                .map(change => this.analyzeChangeWithNarration(change))
                .filter(change => change.includeInReport);

            if (analyzedChanges.length > 0) {
                const firstChange = analyzedChanges[0];
                itemData.push({
                    itemName: firstChange.item_name,
                    sectionName: firstChange.section_name,
                    changeType: firstChange.changeType,
                    narration: `${firstChange.item_name} ${firstChange.changeType.toLowerCase()} in ${this.currentMonth} Payslip for the following:`,
                    affectedEmployees: analyzedChanges.map(change => ({
                        employeeId: change.employee_id,
                        employeeName: change.employee_name,
                        department: change.department || change.section_name,
                        changeDetails: change.narration
                    })),
                    employeeCount: analyzedChanges.length
                });
            }
        });

        return itemData.sort((a, b) => a.itemName.localeCompare(b.itemName));
    }

    /**
     * Determine priority based on section
     */
    determinePriority(sectionName) {
        if (!sectionName) return 'LOW';

        const section = sectionName.toLowerCase();

        for (const [priority, sections] of Object.entries(this.rules.priority)) {
            if (sections.some(s => section.includes(s.toLowerCase()))) {
                return priority;
            }
        }

        return 'LOW';
    }

    /**
     * ADVANCED: Detect Promotions according to specification
     */
    detectPromotionsAdvanced(changes) {
        const employeeGroups = this.groupChangesByEmployee(changes);
        const promotions = { ministers: [], staff: [] };

        Object.entries(employeeGroups).forEach(([employeeId, empChanges]) => {
            const isMinister = this.isMinisterDepartment(empChanges);
            const hasBasicSalaryIncrease = this.hasBasicSalaryIncrease(empChanges);
            const hasJobTitleChange = this.hasJobTitleChange(empChanges);

            let isPromotion = false;

            if (isMinister && hasJobTitleChange) {
                // MINISTER PROMOTION = Job title change only for Ministers
                isPromotion = true;
                promotions.ministers.push(this.createPromotionRecord(empChanges, 'MINISTER'));
            } else if (!isMinister && hasBasicSalaryIncrease && hasJobTitleChange) {
                // STAFF PROMOTION = Basic salary increase + Job Title change
                isPromotion = true;
                promotions.staff.push(this.createPromotionRecord(empChanges, 'STAFF'));
            }
        });

        return promotions;
    }

    /**
     * ADVANCED: Detect Transfers according to specification
     */
    detectTransfersAdvanced(changes) {
        const employeeGroups = this.groupChangesByEmployee(changes);
        const transfers = { ministers: [], staff: [] };

        Object.entries(employeeGroups).forEach(([employeeId, empChanges]) => {
            const departmentChange = this.hasDepartmentChange(empChanges);

            if (departmentChange) {
                const isMinister = this.isMinisterDepartment(empChanges);

                if (isMinister) {
                    transfers.ministers.push(this.createTransferRecord(empChanges, 'MINISTER'));
                } else {
                    transfers.staff.push(this.createTransferRecord(empChanges, 'STAFF'));
                }
            }
        });

        return transfers;
    }

    /**
     * Detect New Employees according to specification
     */
    detectNewEmployees(currentChanges, previousData) {
        const currentEmployees = this.extractUniqueEmployees(currentChanges);
        const previousEmployees = this.extractUniqueEmployees(previousData);

        return currentEmployees.filter(current =>
            !previousEmployees.find(prev =>
                prev.employee_id === current.employee_id ||
                prev.employee_name === current.employee_name
            )
        );
    }

    /**
     * Detect Removed Employees according to specification
     */
    detectRemovedEmployees(currentChanges, previousData) {
        const currentEmployees = this.extractUniqueEmployees(currentChanges);
        const previousEmployees = this.extractUniqueEmployees(previousData);

        return previousEmployees.filter(prev =>
            !currentEmployees.find(current =>
                current.employee_id === prev.employee_id ||
                current.employee_name === prev.employee_name
            )
        );
    }

    /**
     * Analyze Loan Changes according to specification
     */
    analyzeLoanChanges(changes) {
        const loanChanges = changes.filter(change => this.isLoanChange(change));
        const reportableLoanChanges = [];

        loanChanges.forEach(change => {
            const rules = this.rules.loanRules.reportableChanges;

            if (rules.balanceIncrease.condition(change)) {
                reportableLoanChanges.push({
                    ...change,
                    loanType: 'balance_increase',
                    narration: rules.balanceIncrease.template
                        .replace('{loan_name}', change.item_name)
                        .replace('{prev_value}', this.formatCurrency(change.previous_value))
                        .replace('{curr_value}', this.formatCurrency(change.current_value))
                        .replace('{month}', this.currentMonth)
                });
            }

            if (rules.currentDeductionIncrease.condition(change)) {
                reportableLoanChanges.push({
                    ...change,
                    loanType: 'deduction_increase',
                    narration: rules.currentDeductionIncrease.template
                        .replace('{loan_name}', change.item_name)
                        .replace('{prev_value}', this.formatCurrency(change.previous_value))
                        .replace('{curr_value}', this.formatCurrency(change.current_value))
                });
            }
        });

        return reportableLoanChanges;
    }

    /**
     * Generate Executive Summary according to specification
     */
    generateExecutiveSummary(results) {
        const individualAnomalies = results.analyzedChanges.filter(c => c.includeInReport).length;
        const smallBulkChanges = 0; // Will be calculated based on bulk analysis
        const significantChanges = individualAnomalies + smallBulkChanges;

        return {
            significantChangesDetected: significantChanges,
            highPriorityChanges: results.analyzedChanges.filter(c => c.priority === 'HIGH' && c.includeInReport).length,
            moderatePriorityChanges: results.analyzedChanges.filter(c => c.priority === 'MODERATE' && c.includeInReport).length,
            lowPriorityChanges: results.analyzedChanges.filter(c => c.priority === 'LOW' && c.includeInReport).length,
            promotionsDetected: (results.promotions.ministers?.length || 0) + (results.promotions.staff?.length || 0),
            transfersDetected: (results.transfers.ministers?.length || 0) + (results.transfers.staff?.length || 0),
            newEmployees: results.newEmployees?.length || 0,
            removedEmployees: results.removedEmployees?.length || 0
        };
    }

    // ===== HELPER METHODS =====

    /**
     * Group changes by employee
     */
    groupChangesByEmployee(changes) {
        return changes.reduce((groups, change) => {
            const key = change.employee_id || change.employee_name;
            if (!groups[key]) groups[key] = [];
            groups[key].push(change);
            return groups;
        }, {});
    }

    /**
     * Group changes by item for item-based reporting
     */
    groupChangesByItem(changes) {
        return changes.reduce((groups, change) => {
            const key = `${change.section_name}_${change.item_name}`;
            if (!groups[key]) groups[key] = [];
            groups[key].push(change);
            return groups;
        }, {});
    }

    /**
     * Analyze individual change with narration
     */
    analyzeChangeWithNarration(change) {
        const changeType = this.determineChangeType(change);
        const narration = this.generateNarration(change, changeType);
        const priority = this.determinePriority(change.section_name);

        return {
            ...change,
            changeType,
            narration,
            priority,
            includeInReport: changeType !== 'NO_CHANGE' && narration !== null,
            isLoanChange: this.isLoanChange(change)
        };
    }

    /**
     * Check if change is a loan-related change
     */
    isLoanChange(change) {
        const itemName = (change.item_name || '').toLowerCase();
        return this.rules.loanRules.loanKeywords.some(keyword =>
            itemName.includes(keyword.toLowerCase())
        );
    }

    /**
     * Check if change is a department change
     */
    isDepartmentChange(change) {
        const itemName = (change.item_name || '').toLowerCase();
        return itemName.includes('department') || itemName.includes('ministry');
    }

    /**
     * Extract unique employees from data (CRITICAL for NEW/REMOVED employee detection)
     */
    extractUniqueEmployees(data) {
        const employeeMap = new Map();

        data.forEach(item => {
            const key = item.employee_id || item.employee_name;
            if (key && !employeeMap.has(key)) {
                employeeMap.set(key, {
                    employee_id: item.employee_id,
                    employee_name: item.employee_name,
                    department: item.department || item.section_name
                });
            }
        });

        return Array.from(employeeMap.values());
    }

    /**
     * Check if employee department contains "Ministers"
     */
    isMinisterDepartment(empChanges) {
        return empChanges.some(change =>
            (change.department || change.section_name || '').toLowerCase().includes('minister')
        );
    }

    /**
     * Check if employee has basic salary increase
     */
    hasBasicSalaryIncrease(empChanges) {
        return empChanges.some(change => {
            const itemName = (change.item_name || '').toLowerCase();
            const sectionName = (change.section_name || '').toLowerCase();
            const hasIncrease = parseFloat(change.current_value || 0) > parseFloat(change.previous_value || 0);

            return hasIncrease && (
                itemName.includes('basic salary') ||
                itemName.includes('salary') ||
                sectionName.includes('earnings')
            );
        });
    }

    /**
     * Check if employee has job title change
     */
    hasJobTitleChange(empChanges) {
        return empChanges.some(change => {
            const itemName = (change.item_name || '').toLowerCase();
            return itemName.includes('title') ||
                   itemName.includes('position') ||
                   itemName.includes('designation') ||
                   itemName.includes('rank');
        });
    }

    /**
     * Check if employee has department change
     */
    hasDepartmentChange(empChanges) {
        return empChanges.some(change => this.isDepartmentChange(change));
    }

    /**
     * Check if employee has promotion-related changes
     */
    hasPromotionChanges(empChanges) {
        const isMinister = this.isMinisterDepartment(empChanges);
        const hasBasicSalaryIncrease = this.hasBasicSalaryIncrease(empChanges);
        const hasJobTitleChange = this.hasJobTitleChange(empChanges);

        if (isMinister && hasJobTitleChange) return true;
        if (!isMinister && hasBasicSalaryIncrease && hasJobTitleChange) return true;

        return false;
    }

    /**
     * Check if employee has transfer-related changes
     */
    hasTransferChanges(empChanges) {
        return this.hasDepartmentChange(empChanges);
    }

    /**
     * Create promotion record according to specification format
     */
    createPromotionRecord(empChanges, type) {
        const employee = empChanges[0];
        const titleChanges = empChanges.filter(change => this.hasJobTitleChange([change]));

        let oldTitle = 'Unknown';
        let newTitle = 'Unknown';

        if (titleChanges.length > 0) {
            oldTitle = titleChanges[0].previous_value || 'Unknown';
            newTitle = titleChanges[0].current_value || 'Unknown';
        }

        return {
            employeeId: employee.employee_id,
            employeeName: employee.employee_name,
            department: employee.department || employee.section_name,
            oldTitle,
            newTitle,
            type,
            formattedText: `${employee.employee_id}: ${employee.employee_name} – ${employee.department || employee.section_name}: ${oldTitle} to ${newTitle}`
        };
    }

    /**
     * Create transfer record according to specification format
     */
    createTransferRecord(empChanges, type) {
        const employee = empChanges[0];
        const deptChanges = empChanges.filter(change => this.isDepartmentChange(change));

        let oldDept = 'Unknown';
        let newDept = 'Unknown';

        if (deptChanges.length > 0) {
            oldDept = deptChanges[0].previous_value || 'Unknown';
            newDept = deptChanges[0].current_value || 'Unknown';
        }

        return {
            employeeId: employee.employee_id,
            employeeName: employee.employee_name,
            department: employee.department || employee.section_name,
            oldDepartment: oldDept,
            newDepartment: newDept,
            type,
            formattedText: `${employee.employee_id}: ${employee.employee_name} – ${employee.department || employee.section_name}: From ${oldDept} TO ${newDept}`
        };
    }

    /**
     * Format currency according to specification
     */
    formatCurrency(value) {
        const numValue = parseFloat(value) || 0;
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS',
            minimumFractionDigits: 2
        }).format(numValue);
    }

    /**
     * Detect promotions in changes
     */
    detectPromotions(changes) {
        const promotions = [];
        const rules = this.rules.promotionDetection;
        
        // Group changes by employee
        const employeeChanges = this.groupChangesByEmployee(changes);
        
        Object.entries(employeeChanges).forEach(([employeeId, empChanges]) => {
            const salaryIncrease = this.calculateSalaryIncrease(empChanges);
            const titleChange = this.detectTitleChange(empChanges);
            
            if (salaryIncrease >= rules.salaryIncreaseThreshold || titleChange) {
                promotions.push({
                    employeeId,
                    employeeName: empChanges[0].employee_name,
                    salaryIncrease,
                    titleChange,
                    changes: empChanges,
                    type: salaryIncrease >= rules.ministerPromotionThreshold ? 'MINISTER' : 'STAFF'
                });
            }
        });
        
        return promotions;
    }

    /**
     * Detect transfers in changes
     */
    detectTransfers(changes) {
        const transfers = [];
        const employeeChanges = this.groupChangesByEmployee(changes);
        
        Object.entries(employeeChanges).forEach(([employeeId, empChanges]) => {
            const departmentChange = this.detectDepartmentChange(empChanges);
            
            if (departmentChange) {
                transfers.push({
                    employeeId,
                    employeeName: empChanges[0].employee_name,
                    departmentChange,
                    changes: empChanges,
                    type: 'TRANSFER'
                });
            }
        });
        
        return transfers;
    }

    // HELPER METHODS

    groupChangesByEmployee(changes) {
        return changes.reduce((groups, change) => {
            const key = change.employee_id;
            if (!groups[key]) groups[key] = [];
            groups[key].push(change);
            return groups;
        }, {});
    }

    calculateSalaryIncrease(changes) {
        const salaryChanges = changes.filter(c => 
            c.section_name?.toLowerCase().includes('earnings') ||
            c.section_name?.toLowerCase().includes('salary')
        );
        
        return salaryChanges.reduce((total, change) => {
            const prev = parseFloat(change.previous_value) || 0;
            const curr = parseFloat(change.current_value) || 0;
            return total + (curr - prev);
        }, 0);
    }

    detectTitleChange(changes) {
        const titleChanges = changes.filter(c => 
            c.item_name?.toLowerCase().includes('title') ||
            c.item_name?.toLowerCase().includes('position')
        );
        
        return titleChanges.length > 0;
    }

    detectDepartmentChange(changes) {
        const deptChanges = changes.filter(c => 
            c.section_name?.toLowerCase().includes('department') ||
            c.item_name?.toLowerCase().includes('department')
        );
        
        return deptChanges.length > 0 ? deptChanges[0] : null;
    }

    generateSummary(results) {
        return {
            totalChanges: results.categorized.length,
            highPriorityChanges: results.categorized.filter(c => c.priority === 'HIGH').length,
            moderatePriorityChanges: results.categorized.filter(c => c.priority === 'MODERATE').length,
            lowPriorityChanges: results.categorized.filter(c => c.priority === 'LOW').length,
            promotionsDetected: results.promotions.length,
            transfersDetected: results.transfers.length,
            bulkChangesCount: Object.keys(results.bulkAnalysis).length
        };
    }
}

// Export for use in Final Report Interface
window.BusinessRulesEngine = BusinessRulesEngine;
