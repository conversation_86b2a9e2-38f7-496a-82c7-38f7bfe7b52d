/**
 * Business Rules Engine for Final Report Interface
 * Centralizes all business logic for payroll change processing and report generation
 */

class BusinessRulesEngine {
    constructor() {
        this.rules = {
            priority: this.initializePriorityRules(),
            changeDetection: this.initializeChangeDetectionRules(),
            bulkCategorization: this.initializeBulkCategorizationRules(),
            reportGeneration: this.initializeReportGenerationRules(),
            promotionDetection: this.initializePromotionDetectionRules(),
            transferDetection: this.initializeTransferDetectionRules()
        };
        
        console.log('🎯 Business Rules Engine initialized');
    }

    // PRIORITY CLASSIFICATION RULES
    initializePriorityRules() {
        return {
            HIGH: [
                'Personal Details',
                'Earnings', 
                'Deductions',
                'Bank Details'
            ],
            MODERATE: [
                'Loans',
                'Employee Bank Details'
            ],
            LOW: [
                'Employer Contributions',
                'Allowances',
                'Other Benefits'
            ]
        };
    }

    // CHANGE DETECTION RULES
    initializeChangeDetectionRules() {
        return {
            significantThresholds: {
                salary: 1000,      // Changes above 1000 are significant
                allowance: 500,    // Changes above 500 are significant
                deduction: 200     // Changes above 200 are significant
            },
            changeTypes: {
                INCREASE: 'increase',
                DECREASE: 'decrease', 
                NEW: 'new',
                REMOVED: 'removed',
                NO_CHANGE: 'no_change'
            },
            excludeFromReporting: [
                'Staff Credit Union',
                'Routine Adjustment',
                'Standard Contribution'
            ]
        };
    }

    // BULK CATEGORIZATION RULES
    initializeBulkCategorizationRules() {
        return {
            individual: {
                maxChanges: 1,
                description: 'Individual Anomaly'
            },
            small: {
                minChanges: 2,
                maxChanges: 10,
                description: 'Small Bulk Change'
            },
            medium: {
                minChanges: 11,
                maxChanges: 50,
                description: 'Medium Bulk Change'
            },
            large: {
                minChanges: 51,
                maxChanges: Infinity,
                description: 'Large Bulk Change'
            }
        };
    }

    // REPORT GENERATION RULES
    initializeReportGenerationRules() {
        return {
            employeeBased: {
                groupBy: 'employee',
                sortBy: 'employee_name',
                includeAppendix: true,
                sections: ['findings', 'promotions', 'transfers', 'new_employees', 'removed_employees']
            },
            itemBased: {
                groupBy: 'item_type',
                sortBy: 'section_name',
                includeAppendix: false,
                sections: ['findings', 'bulk_changes', 'significant_changes']
            },
            formatting: {
                dateFormat: 'MMMM YYYY',
                numberFormat: 'currency',
                includeExecutiveSummary: true
            }
        };
    }

    // PROMOTION DETECTION RULES
    initializePromotionDetectionRules() {
        return {
            salaryIncreaseThreshold: 5000,  // Minimum salary increase to consider promotion
            titleChangeKeywords: [
                'promoted', 'senior', 'chief', 'head', 'director', 
                'manager', 'supervisor', 'coordinator', 'lead'
            ],
            gradeChangePattern: /grade\s*(\d+)\s*to\s*grade\s*(\d+)/i,
            ministerPromotionThreshold: 10000  // Higher threshold for ministers
        };
    }

    // TRANSFER DETECTION RULES
    initializeTransferDetectionRules() {
        return {
            departmentChangeKeywords: [
                'ministry', 'department', 'division', 'unit', 'office', 'bureau'
            ],
            locationChangeKeywords: [
                'transferred', 'moved', 'relocated', 'assigned'
            ],
            costCenterPattern: /cost\s*center\s*(\w+)\s*to\s*(\w+)/i
        };
    }

    // MAIN PROCESSING METHODS

    /**
     * Process payroll changes according to business rules
     * @param {Array} changes - Array of payroll changes
     * @param {Object} config - Processing configuration
     * @returns {Object} Processed results with categorization
     */
    processChanges(changes, config = {}) {
        console.log(`🔄 Processing ${changes.length} changes with business rules...`);
        
        const results = {
            categorized: this.categorizeChanges(changes),
            prioritized: this.prioritizeChanges(changes),
            bulkAnalysis: this.analyzeBulkChanges(changes),
            promotions: this.detectPromotions(changes),
            transfers: this.detectTransfers(changes),
            summary: {}
        };

        results.summary = this.generateSummary(results);
        
        console.log('✅ Business rules processing complete:', results.summary);
        return results;
    }

    /**
     * Categorize changes by type and significance
     */
    categorizeChanges(changes) {
        return changes.map(change => {
            const category = this.determineChangeCategory(change);
            const priority = this.determinePriority(change.section_name);
            const significance = this.determineSignificance(change);
            
            return {
                ...change,
                category,
                priority,
                significance,
                includeInReport: this.shouldIncludeInReport(change)
            };
        });
    }

    /**
     * Determine change category based on business rules
     */
    determineChangeCategory(change) {
        const prevValue = parseFloat(change.previous_value) || 0;
        const currValue = parseFloat(change.current_value) || 0;
        const difference = currValue - prevValue;

        if (prevValue === 0 && currValue > 0) return 'NEW';
        if (prevValue > 0 && currValue === 0) return 'REMOVED';
        if (difference > 0) return 'INCREASE';
        if (difference < 0) return 'DECREASE';
        return 'NO_CHANGE';
    }

    /**
     * Determine priority based on section
     */
    determinePriority(sectionName) {
        if (!sectionName) return 'LOW';
        
        const section = sectionName.toLowerCase();
        
        for (const [priority, sections] of Object.entries(this.rules.priority)) {
            if (sections.some(s => section.includes(s.toLowerCase()))) {
                return priority;
            }
        }
        
        return 'LOW';
    }

    /**
     * Determine significance of change
     */
    determineSignificance(change) {
        const prevValue = parseFloat(change.previous_value) || 0;
        const currValue = parseFloat(change.current_value) || 0;
        const difference = Math.abs(currValue - prevValue);
        
        const section = change.section_name?.toLowerCase() || '';
        const thresholds = this.rules.changeDetection.significantThresholds;
        
        if (section.includes('salary') || section.includes('earnings')) {
            return difference >= thresholds.salary ? 'SIGNIFICANT' : 'MINOR';
        }
        if (section.includes('allowance')) {
            return difference >= thresholds.allowance ? 'SIGNIFICANT' : 'MINOR';
        }
        if (section.includes('deduction')) {
            return difference >= thresholds.deduction ? 'SIGNIFICANT' : 'MINOR';
        }
        
        return difference > 0 ? 'MINOR' : 'NONE';
    }

    /**
     * Check if change should be included in report
     */
    shouldIncludeInReport(change) {
        const itemName = change.item_name?.toLowerCase() || '';
        const excludeList = this.rules.changeDetection.excludeFromReporting;
        
        return !excludeList.some(exclude => 
            itemName.includes(exclude.toLowerCase())
        );
    }

    /**
     * Prioritize changes for report ordering
     */
    prioritizeChanges(changes) {
        return changes.sort((a, b) => {
            const priorityOrder = { HIGH: 3, MODERATE: 2, LOW: 1 };
            const aPriority = priorityOrder[this.determinePriority(a.section_name)] || 1;
            const bPriority = priorityOrder[this.determinePriority(b.section_name)] || 1;
            
            if (aPriority !== bPriority) return bPriority - aPriority;
            
            // Secondary sort by significance
            const significanceOrder = { SIGNIFICANT: 2, MINOR: 1, NONE: 0 };
            const aSignificance = significanceOrder[this.determineSignificance(a)] || 0;
            const bSignificance = significanceOrder[this.determineSignificance(b)] || 0;
            
            return bSignificance - aSignificance;
        });
    }

    /**
     * Analyze bulk changes
     */
    analyzeBulkChanges(changes) {
        const groupedChanges = this.groupChangesByType(changes);
        const bulkAnalysis = {};
        
        Object.entries(groupedChanges).forEach(([type, typeChanges]) => {
            const count = typeChanges.length;
            const category = this.categorizeBulkSize(count);
            
            bulkAnalysis[type] = {
                count,
                category: category.description,
                changes: typeChanges,
                isSignificant: count >= this.rules.bulkCategorization.medium.minChanges
            };
        });
        
        return bulkAnalysis;
    }

    /**
     * Group changes by type for bulk analysis
     */
    groupChangesByType(changes) {
        return changes.reduce((groups, change) => {
            const key = `${change.section_name}_${change.item_name}`;
            if (!groups[key]) groups[key] = [];
            groups[key].push(change);
            return groups;
        }, {});
    }

    /**
     * Categorize bulk size
     */
    categorizeBulkSize(count) {
        const rules = this.rules.bulkCategorization;
        
        if (count <= rules.individual.maxChanges) return rules.individual;
        if (count <= rules.small.maxChanges) return rules.small;
        if (count <= rules.medium.maxChanges) return rules.medium;
        return rules.large;
    }

    /**
     * Detect promotions in changes
     */
    detectPromotions(changes) {
        const promotions = [];
        const rules = this.rules.promotionDetection;
        
        // Group changes by employee
        const employeeChanges = this.groupChangesByEmployee(changes);
        
        Object.entries(employeeChanges).forEach(([employeeId, empChanges]) => {
            const salaryIncrease = this.calculateSalaryIncrease(empChanges);
            const titleChange = this.detectTitleChange(empChanges);
            
            if (salaryIncrease >= rules.salaryIncreaseThreshold || titleChange) {
                promotions.push({
                    employeeId,
                    employeeName: empChanges[0].employee_name,
                    salaryIncrease,
                    titleChange,
                    changes: empChanges,
                    type: salaryIncrease >= rules.ministerPromotionThreshold ? 'MINISTER' : 'STAFF'
                });
            }
        });
        
        return promotions;
    }

    /**
     * Detect transfers in changes
     */
    detectTransfers(changes) {
        const transfers = [];
        const employeeChanges = this.groupChangesByEmployee(changes);
        
        Object.entries(employeeChanges).forEach(([employeeId, empChanges]) => {
            const departmentChange = this.detectDepartmentChange(empChanges);
            
            if (departmentChange) {
                transfers.push({
                    employeeId,
                    employeeName: empChanges[0].employee_name,
                    departmentChange,
                    changes: empChanges,
                    type: 'TRANSFER'
                });
            }
        });
        
        return transfers;
    }

    // HELPER METHODS

    groupChangesByEmployee(changes) {
        return changes.reduce((groups, change) => {
            const key = change.employee_id;
            if (!groups[key]) groups[key] = [];
            groups[key].push(change);
            return groups;
        }, {});
    }

    calculateSalaryIncrease(changes) {
        const salaryChanges = changes.filter(c => 
            c.section_name?.toLowerCase().includes('earnings') ||
            c.section_name?.toLowerCase().includes('salary')
        );
        
        return salaryChanges.reduce((total, change) => {
            const prev = parseFloat(change.previous_value) || 0;
            const curr = parseFloat(change.current_value) || 0;
            return total + (curr - prev);
        }, 0);
    }

    detectTitleChange(changes) {
        const titleChanges = changes.filter(c => 
            c.item_name?.toLowerCase().includes('title') ||
            c.item_name?.toLowerCase().includes('position')
        );
        
        return titleChanges.length > 0;
    }

    detectDepartmentChange(changes) {
        const deptChanges = changes.filter(c => 
            c.section_name?.toLowerCase().includes('department') ||
            c.item_name?.toLowerCase().includes('department')
        );
        
        return deptChanges.length > 0 ? deptChanges[0] : null;
    }

    generateSummary(results) {
        return {
            totalChanges: results.categorized.length,
            highPriorityChanges: results.categorized.filter(c => c.priority === 'HIGH').length,
            moderatePriorityChanges: results.categorized.filter(c => c.priority === 'MODERATE').length,
            lowPriorityChanges: results.categorized.filter(c => c.priority === 'LOW').length,
            promotionsDetected: results.promotions.length,
            transfersDetected: results.transfers.length,
            bulkChangesCount: Object.keys(results.bulkAnalysis).length
        };
    }
}

// Export for use in Final Report Interface
window.BusinessRulesEngine = BusinessRulesEngine;
