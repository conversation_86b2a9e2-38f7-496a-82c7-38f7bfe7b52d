OVERVIEW
........

I need you to enhance the Pre-reporting UI to reflect our current need of a dual reporting UI but same page and this is the idea:

1. I want the Pre-reporting UI to be a dual reporting system where by the current pre-reporting system becomes traditional data source for both pre-reporting and Final Reporting on which the new feature "Smart Reporter" will depend on although users can print a Report from it the traditional pre-reporting system.

2. The enhancement aspect to the pre-reporting interface will be to add a toggle so the interface can switch between PRE-REPORTING side (the current face), and the "Smart Reporter" (the new side but same page)

3. SO the existing Pre-reporting keeps all its core function with the following adjustment: A. its existing GENERATE REPORT button which will be renamed "Generate-PRE-REPORT".
   B. The pre-reporting side will have a drop down for sorting pre-reporting data received by: Employees, Change Flag (will group data by INCREASE, DECREASE, REMOVED, NEW, NO_CHANGE), Priority: High, Moderate, Low, and  Bulk category: individual anomalies, small Bulk change, Medium Bulk change, Large Bulk Change. 

c. A New shared Feature to add for both will be a DUAL REPORT CONFIGURATION which is the same as the Report configuration we have in the Payroll Audit tab in the Pre-reporting interface (this means the Reporting configuration feature in the Payroll Audit tab will be removed) it will have only Generated By, and Designation) it will be placed above the so that it works for both sides when details are entered.

4. The Smart Reporter side will be a powerful reporting system, powered by  Reporting algorithms, ai and Business rules for the generation of a well designed Final Report. it will have the following features:
A. A button Called "Generate-FINAL REPORT"

B. A report type drop down selection for either 1. Employee Based Report or 2. Item-Based Report. (will show you sample of each report). Note: Report types are: WORD, PDF, EXCEL(Excel report will have separate rules and specs)

C. Business and Reporting rule Pane for addition and removal.





Reporting rules: General rules for Employee-Based Report
.......................................................
The following are key examples of key reporting rule that is dependent on the pre-reporting data:

1. Changes found are reported based on individual employees
2. Reporting follows this format; changed item name comes first, followed by narration e.g.: SCHOLARSHIP FUND increased from 5.00 to 20.00 in July 2025 (increase of 15.00)
3. The report focus is mainly on the current month.eg: if Previous Department was FNANCE, and Current is SECRETARIAT, we report like: 

COP1253: ABUAKWA AREA - MINISTERS - AMOS SIGNAAH
1. Department changed from FINANCE to SECRETRIAT in JULY 2025

4. When an item is added to a persons payslip for the current month the change type is NEW
5. When an item is removed from a person payslip the change type is REMOVED
6. When an Employee is not found in the current payroll of which mainly his Employee No. and Employee Name will not be found in the current payslip the change type is REMOVED
7. When an item value or amount increases the change type is INCREASED
8. When an item value or amount decreases the change type is DECREASED
9. When there is no change in any of the items compared for an employee the change type is NO_CHANGE. however no changes are not to appear in report
10. Note that an employee can have more than one change type all will be reported under their name numbered i order
1i. The attached word document screenshot show the reporting language in the report of course its based on the change type or flags.


Note: Some important payslip rules for Algorithm
.................................................
Loans reporting:
The only change we seek to report on when it comes to LOANS are the following:
a) An increase in Balance B/F of an existing loan. it will be captured as e.g.: SALARY ADVANCE Balance increased from 10000 to 15000 in July 2025
b) An increase of the Current Deduction. captured as e.g.: RENT ADVANCE Current Deduction increased from 5000.00 to 10000  

Promotion reporting: 

1. Promotion: STAFF PROMOTION= Basic salary increase + Job Title change. MINISTER PROMOTION= Job title change only for persons who have  Minsters in their Department value 
2. Transfer: STAFF TRANSFER= Change in Department
2. New Employee: If Employee No. is appearing in current month but not in Previous month i.e. NEW Employee NO. OR Employee Name
3. Removed Employee= Employee Name or Employee No. removed from the current month but was present in previous. 

These rules are not permissive they must be strictly enforced so we get the right report.

Note this: system must Put PROMOTION and TRANSFER as an Appendix to the main report which appears after the main individual reports, and the Algorithm must identify all that fall under the promotion or Transfer rule and list like below

PROMOTIONS MINISTERS

•COP0209: RICK JAMES – ABUAKWA AREA-MINISTERS: Pastor to Apostle
•COP1010: JAMES NARH - KASOA AREA: Overseer to Pastor

PROMOTIONS STAFF

•COP0209: SAMUEL KEN – FINANCE:   Senior Audit Officer to Principal Audit Officer 
•COP1010: JAMES KESSY - PENSIONS: Finance Officer to Senior Finance Officer

 TRANSFERS MINISTER
•COP0209: RICK JAMES – ABUAKWA AREA-MINISTERS: ABUAKWA AREA TO TAFO AREA 
•COP1010: JAMES NARH - KASOA AREA: KASOA AREA to NUNGUA AREA

TRANSFERS STAFF

•COP0209: RICK JAMES – ABUAKWA AREA-MINISTERS: From ABUAKWA AREA TO TAFO AREA 
•COP1010: JAMES NARH - KASOA AREA: KASOA AREA to TWIFO PRASO AREA

Note: The staff information contained in this document are dummy just for explanation is not meant for production.


Reporting rules: General rules for Item-based Reporting
.......................................................

For Item based reporting the report will look like this:

1. SECURITY GUARD ALLOWANCE increased in July 2025 Payslip for the following:

•COP0209: RICK JAMES – ABUAKWA AREA-MINISTERS 
•COP1010: JAMES NARH - KASOA AREA

NB: so basically the Item name and Narration comes followed by listed persons employee no, name and department under that item change. And these are key for us to have an algorithm or apply AI to do the reporting based on the data in the pre-reporting but these information are the blue print that must be followed

WORD and PDF REPORT SPECIFICS (review screenshot for details)
............................................................
Font is Cambria (Body) 14
Main Heading: Calibri (Headings) 26
Period and Generated at: must be auto generated by the system
Generated By: and Designation: will be inherited from the Dual report configuration. 
Significant Changes detected: will be the total number of individual anomalies+ small Bulk changes  in the Pre-reporting
break down for High Priority, Moderate priority, Low Priority will be also gotten from same source.

This must be implemented without breaking any existing feature in the system and must be done highly professionally by putting items into Task list and working on each phase step by step and test implementation. All reports must go to the Report Manger.

