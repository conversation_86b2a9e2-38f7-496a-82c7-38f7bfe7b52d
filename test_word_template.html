<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word Template Engine Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-header {
            text-align: center;
            margin-bottom: 30px;
            color: #1a237e;
        }
        .test-section {
            margin-bottom: 25px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 6px;
        }
        .test-section h3 {
            color: #3f51b5;
            margin-bottom: 15px;
        }
        .btn {
            background: #3f51b5;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        .btn:hover {
            background: #1a237e;
        }
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1>📄 Word Template Engine Test</h1>
            <p>Test the Word document generation functionality matching the exact template format</p>
        </div>

        <div class="test-section">
            <h3>1. Template Engine Initialization</h3>
            <button class="btn" onclick="testInitialization()">Test Initialization</button>
            <div id="init-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>2. Sample Smart Report Data</h3>
            <button class="btn" onclick="generateSampleData()">Generate Sample Data</button>
            <div id="sample-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>3. Word Document Generation</h3>
            <button class="btn" onclick="testWordGeneration()" id="word-gen-btn">Generate Word Document</button>
            <div id="word-result" class="result" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>4. Template Structure Validation</h3>
            <button class="btn" onclick="validateTemplate()">Validate Template Structure</button>
            <div id="template-result" class="result" style="display: none;"></div>
        </div>
    </div>

    <!-- Load the Word Template Engine -->
    <script src="ui/word_template_engine.js"></script>

    <script>
        let wordTemplateEngine = null;
        let sampleSmartReport = null;

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.className = `result ${type}`;
            element.innerHTML = message;
            element.style.display = 'block';
        }

        function testInitialization() {
            try {
                wordTemplateEngine = new WordTemplateEngine();
                showResult('init-result', '✅ Word Template Engine initialized successfully!', 'success');
                console.log('Word Template Engine:', wordTemplateEngine);
            } catch (error) {
                showResult('init-result', `❌ Initialization failed: ${error.message}`, 'error');
                console.error('Initialization error:', error);
            }
        }

        function generateSampleData() {
            try {
                sampleSmartReport = {
                    metadata: {
                        generatedBy: 'Samuel Asiedu',
                        designation: 'Sr. Audit Officer',
                        reportType: 'employeeBased',
                        outputFormat: 'word',
                        generatedAt: new Date().toISOString(),
                        businessRulesApplied: true
                    },
                    summary: {
                        totalChanges: 4,
                        highPriorityChanges: 3,
                        moderatePriorityChanges: 1,
                        lowPriorityChanges: 0,
                        promotionsDetected: 0,
                        transfersDetected: 0,
                        bulkChangesCount: 0
                    },
                    findings: {
                        totalChanges: 4,
                        highPriorityChanges: [
                            {
                                employee_id: 'COP2209',
                                employee_name: 'APPIAH-AIDOO A',
                                section_name: 'ABUAKWA AREA-MINISTERS',
                                item_name: 'SCHOLARSHIP FUND',
                                previous_value: 5.00,
                                current_value: 20.00,
                                category: 'INCREASE',
                                priority: 'HIGH'
                            },
                            {
                                employee_id: 'COP2626',
                                employee_name: 'SAMUEL ASIEDU',
                                section_name: 'AUDIT, MONITORING & EVALUATION',
                                item_name: 'RESPONSIBILITY HEADS',
                                previous_value: 2000,
                                current_value: 1500,
                                category: 'DECREASE',
                                priority: 'HIGH'
                            },
                            {
                                employee_id: 'COP2524',
                                employee_name: 'MARK ANTHONY',
                                section_name: 'FINANCE',
                                item_name: 'SECURITY GUARD ALLOWANCE',
                                previous_value: 800.00,
                                current_value: 0,
                                category: 'REMOVED',
                                priority: 'HIGH'
                            }
                        ],
                        moderatePriorityChanges: [
                            {
                                employee_id: 'COP4040',
                                employee_name: 'EBENEZER ARHIN',
                                section_name: 'SECRETARIAT',
                                item_name: 'NEW LAND ALLOWANCE',
                                previous_value: 0,
                                current_value: 700.00,
                                category: 'NEW',
                                priority: 'MODERATE'
                            }
                        ],
                        lowPriorityChanges: []
                    },
                    specialFindings: {
                        promotions: [],
                        transfers: [],
                        bulkChanges: []
                    },
                    processedChanges: [
                        {
                            employee_id: 'COP1255',
                            employee_name: 'ABUAKWA AREA',
                            section_name: 'MINISTERS - AMOS SIGNAAH ELIA ADAMS',
                            category: 'NEW'
                        },
                        {
                            employee_id: 'COP1425',
                            employee_name: 'ABUAKWA AREA',
                            section_name: 'STAFF - ROBERT SAKYI-KAAH',
                            category: 'NEW'
                        },
                        {
                            employee_id: 'COP1438',
                            employee_name: 'ABUAKWA AREA',
                            section_name: 'MINISTERS - SAMUEL AMO BOATENG',
                            category: 'NEW'
                        },
                        {
                            employee_id: 'COP1513',
                            employee_name: 'ABUAKWA AREA',
                            section_name: 'MINISTERS - KPAKPOFIO ADOTEY',
                            category: 'REMOVED'
                        },
                        {
                            employee_id: 'COP1658',
                            employee_name: 'ABUAKWA AREA',
                            section_name: 'MINISTERS - ISAAC YVES SOWAH',
                            category: 'REMOVED'
                        },
                        {
                            employee_id: 'PW0271',
                            employee_name: 'AFLAO AREA-WID/PENSIONS',
                            section_name: 'MASLEY SETH GIDEON',
                            category: 'REMOVED'
                        }
                    ]
                };

                showResult('sample-result', 
                    `✅ Sample smart report data generated successfully!\n` +
                    `📊 Total Changes: ${sampleSmartReport.summary.totalChanges}\n` +
                    `🔴 High Priority: ${sampleSmartReport.summary.highPriorityChanges}\n` +
                    `🟡 Moderate Priority: ${sampleSmartReport.summary.moderatePriorityChanges}\n` +
                    `👤 Generated By: ${sampleSmartReport.metadata.generatedBy}\n` +
                    `📋 Designation: ${sampleSmartReport.metadata.designation}`, 
                    'success'
                );
                console.log('Sample Smart Report:', sampleSmartReport);
            } catch (error) {
                showResult('sample-result', `❌ Sample data generation failed: ${error.message}`, 'error');
                console.error('Sample data error:', error);
            }
        }

        async function testWordGeneration() {
            if (!wordTemplateEngine) {
                showResult('word-result', '❌ Please initialize the Word Template Engine first', 'error');
                return;
            }

            if (!sampleSmartReport) {
                showResult('word-result', '❌ Please generate sample data first', 'error');
                return;
            }

            try {
                const btn = document.getElementById('word-gen-btn');
                btn.disabled = true;
                btn.textContent = '⏳ Generating Word Document...';

                showResult('word-result', '⏳ Generating Word document...', 'info');

                const wordDocument = await wordTemplateEngine.generateDownloadableWord(sampleSmartReport);
                
                console.log('Generated Word Document:', wordDocument);

                // Create download link
                const downloadLink = document.createElement('a');
                downloadLink.href = wordDocument.url;
                downloadLink.download = wordDocument.filename;
                downloadLink.style.display = 'none';
                
                // Add to DOM and trigger download
                document.body.appendChild(downloadLink);
                downloadLink.click();
                document.body.removeChild(downloadLink);

                showResult('word-result', 
                    `✅ Word document generated successfully!\n` +
                    `📄 Filename: ${wordDocument.filename}\n` +
                    `📊 Size: ${(wordDocument.size / 1024).toFixed(2)} KB\n` +
                    `💾 Download started automatically`, 
                    'success'
                );

                btn.disabled = false;
                btn.textContent = '📄 Generate Word Document';

            } catch (error) {
                showResult('word-result', `❌ Word generation failed: ${error.message}`, 'error');
                console.error('Word generation error:', error);

                const btn = document.getElementById('word-gen-btn');
                btn.disabled = false;
                btn.textContent = '📄 Generate Word Document';
            }
        }

        function validateTemplate() {
            if (!wordTemplateEngine) {
                showResult('template-result', '❌ Please initialize the Word Template Engine first', 'error');
                return;
            }

            try {
                const template = wordTemplateEngine.templateStructure;
                
                const validation = {
                    hasHeader: !!template.header,
                    hasInfoTable: !!template.infoTable,
                    hasSections: !!template.sections,
                    hasFooter: !!template.footer,
                    headerHasTitle: !!template.header?.title,
                    infoTableHasColumns: Array.isArray(template.infoTable?.columns),
                    sectionsHasFindingsAndObservations: !!template.sections?.findingsAndObservations,
                    sectionsHasNewEmployees: !!template.sections?.newEmployees,
                    sectionsHasRemovedEmployees: !!template.sections?.removedEmployees
                };

                const validationResults = Object.entries(validation)
                    .map(([key, value]) => `${value ? '✅' : '❌'} ${key}: ${value}`)
                    .join('\n');

                const allValid = Object.values(validation).every(v => v);

                showResult('template-result', 
                    `Template Structure Validation:\n${validationResults}\n\n` +
                    `Overall Status: ${allValid ? '✅ VALID' : '❌ INVALID'}`, 
                    allValid ? 'success' : 'error'
                );

                console.log('Template Structure:', template);
                console.log('Validation Results:', validation);

            } catch (error) {
                showResult('template-result', `❌ Template validation failed: ${error.message}`, 'error');
                console.error('Template validation error:', error);
            }
        }

        // Auto-initialize on page load
        window.addEventListener('load', () => {
            console.log('🧪 Word Template Engine Test Page Loaded');
            testInitialization();
        });
    </script>
</body>
</html>
