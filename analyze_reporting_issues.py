#!/usr/bin/env python3
"""
Comprehensive analysis of all reporting issues
"""

import sqlite3
import json

def analyze_all_issues():
    """Analyze all the reporting issues step by step"""
    print('🔍 COMPREHENSIVE ISSUE ANALYSIS')
    print('=' * 50)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # 1. Check current session and reporting period
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        session_result = cursor.fetchone()
        if not session_result:
            print('❌ No current session found')
            return
        
        session_id = session_result[0]
        print(f'✅ Current Session: {session_id}')
        
        # Get session details for reporting period
        cursor.execute('SELECT current_month, current_year, previous_month, previous_year, status FROM audit_sessions WHERE session_id = ?', (session_id,))
        session_details = cursor.fetchone()
        if session_details:
            print(f'\n📅 ISSUE 1 - REPORTING PERIOD:')
            print(f'   Current: {session_details[0]} {session_details[1]}')
            print(f'   Previous: {session_details[2]} {session_details[3]}')
            print(f'   Status: {session_details[4]}')
            print(f'   ✅ Should report for: {session_details[0]} {session_details[1]}')
        
        # 2. Check NO_CHANGE filtering in comparison_results
        print(f'\n🚫 ISSUE 4 - NO_CHANGE ITEMS CHECK:')
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ? AND change_type = "NO_CHANGE"', (session_id,))
        no_change_count = cursor.fetchone()[0]
        print(f'   NO_CHANGE items in comparison_results: {no_change_count}')
        
        if no_change_count > 0:
            print('   ❌ PROBLEM: NO_CHANGE items are included in comparison_results')
            print('   🔧 SOLUTION: Should be filtered out based on Dictionary Manager settings')
        else:
            print('   ✅ NO_CHANGE items properly filtered')
        
        # 3. Check data sources - pre_reporting_results vs comparison_results
        print(f'\n📊 ISSUE 5 - DATA SOURCE PRIORITY:')
        cursor.execute('SELECT COUNT(*) FROM pre_reporting_results WHERE session_id = ?', (session_id,))
        pre_reporting_count = cursor.fetchone()[0]
        print(f'   Pre-reporting results (PRIMARY): {pre_reporting_count}')
        
        cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
        comparison_count = cursor.fetchone()[0]
        print(f'   Comparison results (SECONDARY): {comparison_count}')
        
        if pre_reporting_count > 0:
            print('   ✅ Pre-reporting data available as PRIMARY source')
        else:
            print('   ❌ PROBLEM: No pre-reporting data found')
            print('   🔧 SOLUTION: Should use pre_reporting_results as primary data source')
        
        # 4. Check sample employee data format for headers
        print(f'\n👥 ISSUE 2 - EMPLOYEE HEADER FORMAT:')
        cursor.execute('SELECT employee_id, employee_name, section_name FROM extracted_data WHERE session_id = ? AND period_type = "current" LIMIT 5', (session_id,))
        sample_employees = cursor.fetchall()
        print('   Current format in database:')
        for emp in sample_employees:
            print(f'     {emp[0]}: {emp[1]} - {emp[2]}')
        
        print('   ❌ PROBLEM: Individual headers should NOT include department')
        print('   ✅ CORRECT: COP2626: SAMUEL ASIEDU (no department)')
        print('   ❌ WRONG: COP2626: SAMUEL ASIEDU – AUDIT, MONITORING & EVALUATION')
        
        # 5. Check NEW/REMOVED employee format
        print(f'\n📋 ISSUE 3 - NEW/REMOVED EMPLOYEE FORMAT:')
        print('   Current format being used: EMPLOYEE_ID: EMPLOYEE_NAME – DEPARTMENT')
        print('   ❌ WRONG: COP1255: AMOS SIGNAAH ELIA ADAMS – ABUAKWA AREA - MINISTERS')
        print('   ✅ CORRECT: COP1255: ABUAKWA AREA - MINISTERS - AMOS SIGNAAH ELIA ADAMS')
        print('   📝 Format should be: EMPLOYEE_ID: DEPARTMENT - SECTION - EMPLOYEE_NAME')
        
        # 6. Check Dictionary Manager NO_CHANGE settings
        print(f'\n⚙️ DICTIONARY MANAGER SETTINGS:')
        cursor.execute('SELECT COUNT(*) FROM dictionary_items WHERE include_no_change = 0')
        no_change_disabled_count = cursor.fetchone()[0]
        print(f'   Items with NO_CHANGE disabled: {no_change_disabled_count}')
        
        if no_change_disabled_count > 0:
            cursor.execute('SELECT ds.section_name, di.item_name FROM dictionary_items di JOIN dictionary_sections ds ON di.section_id = ds.id WHERE include_no_change = 0 LIMIT 3')
            no_change_settings = cursor.fetchall()
            print('   Sample items with NO_CHANGE disabled:')
            for setting in no_change_settings:
                print(f'     {setting[0]}.{setting[1]}')
        
        # 7. Summary of all issues
        print(f'\n📋 SUMMARY OF ALL ISSUES:')
        print('   1. ✅ Reporting Period: Correctly identified')
        print('   2. ❌ Employee Headers: Remove department from individual headers')
        print('   3. ❌ NEW/REMOVED Format: Use EMPLOYEE_ID: DEPT - SECTION - NAME')
        print('   4. ❌ NO_CHANGE Items: Should be filtered out based on Dictionary settings')
        print('   5. ❌ Data Source Priority: Should use pre_reporting_results as primary')
        
        conn.close()
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        print(f'Traceback: {traceback.format_exc()}')

if __name__ == "__main__":
    analyze_all_issues()
