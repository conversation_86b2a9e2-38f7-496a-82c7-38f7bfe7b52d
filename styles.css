/* THE PAYROLL AUDITOR - Main Stylesheet */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f5f5f5;
  color: #333;
  height: 100vh;
  margin: 0;
  padding: 0;
}

/* App Container */
.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.app-header {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 15px 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-section i {
  font-size: 32px;
}

.logo-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.logo-section h1 {
  font-size: 24px;
  font-weight: 600;
  margin: 0;
}

.header-subtitle {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.header-created-by,
.header-rights {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
  font-weight: 400;
}

.header-created-by {
  font-weight: 500;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.header-btn {
  background: rgba(255,255,255,0.2);
  border: none;
  color: white;
  padding: 10px;
  border-radius: 5px;
  cursor: pointer;
  transition: background 0.3s;
}

.header-btn:hover {
  background: rgba(255,255,255,0.3);
}

/* Navigation Styles */
.main-nav {
  background: #fff;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  padding: 0 20px;
}

.nav-tab {
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
}

.nav-tab:hover {
  background: #f5f5f5;
  color: #1a237e;
}

.nav-tab.active {
  color: #1a237e;
  border-bottom-color: #1a237e;
  background: #f8f9ff;
}

/* Main Content */
.main-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  padding-bottom: 40px; /* Space for content breathing room */
  min-height: 0; /* Allow flex shrinking */
  scroll-behavior: smooth;
}

.tab-content {
  display: none;
}

.tab-content.active {
  display: block;
}

/* Dashboard Styles */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 20px;
  max-width: 1200px;
}

.dashboard-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  overflow: hidden;
}

.card-header {
  background: #f8f9fa;
  padding: 15px 20px;
  border-bottom: 1px solid #e9ecef;
}

.card-header h3 {
  color: #495057;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-content {
  padding: 20px;
}

/* Button Styles */
.action-btn {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s;
  margin-bottom: 10px;
  width: 100%;
  justify-content: center;
}

.action-btn.primary {
  background: #1a237e;
  color: white;
}

.action-btn.primary:hover {
  background: #303f9f;
  transform: translateY(-1px);
}

.action-btn.secondary {
  background: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.action-btn.secondary:hover {
  background: #e9ecef;
}

.btn {
  padding: 10px 20px;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.3s;
}

.btn.primary {
  background: #1a237e;
  color: white;
}

.btn.primary:hover {
  background: #303f9f;
}

/* Responsive Browse Button Styles */
#browse-current-payroll,
#browse-previous-payroll {
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  pointer-events: auto !important;
  user-select: none !important;
  position: relative;
  overflow: hidden;
}

#browse-current-payroll:hover,
#browse-previous-payroll:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
}

#browse-current-payroll:active,
#browse-previous-payroll:active {
  transform: translateY(0);
  box-shadow: 0 2px 6px rgba(26, 35, 126, 0.2);
}

#browse-current-payroll:disabled,
#browse-previous-payroll:disabled {
  opacity: 0.7;
  cursor: not-allowed !important;
  transform: none !important;
}

/* Instant feedback for button clicks */
#browse-current-payroll::after,
#browse-previous-payroll::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

#browse-current-payroll:active::after,
#browse-previous-payroll:active::after {
  width: 100px;
  height: 100px;
}

/* Start Payroll Audit Button States */
#start-payroll-audit {
  transition: all 0.3s ease !important;
  position: relative;
  overflow: hidden;
}

#start-payroll-audit:not(:disabled) {
  background: #28a745 !important;
  color: white !important;
  cursor: pointer !important;
  opacity: 1 !important;
  transform: scale(1);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

#start-payroll-audit:not(:disabled):hover {
  background: #218838 !important;
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 6px 16px rgba(40, 167, 69, 0.4);
}

#start-payroll-audit:not(:disabled):active {
  transform: translateY(0) scale(0.98);
  box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

#start-payroll-audit:disabled,
#start-payroll-audit.disabled {
  background: #6c757d !important;
  color: #adb5bd !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Requirements Status Styling */
#requirements-status {
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Status Items */
.status-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status-label {
  color: #666;
  font-weight: 500;
}

.status-value {
  color: #333;
  font-weight: 600;
}

/* Activity List with improved scrolling */
.activity-list {
  max-height: 250px;
  overflow-y: auto;
  scroll-behavior: smooth;
  scrollbar-width: thin;
  scrollbar-color: #ccc #f5f5f5;
}

.activity-list::-webkit-scrollbar {
  width: 6px;
}

.activity-list::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb {
  background: #ccc;
  border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
  background: #999;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px 0;
  border-bottom: 1px solid #f0f0f0;
  color: #666;
  transition: all 0.3s ease;
}

.activity-item:hover {
  background-color: #f8f9ff;
  padding-left: 5px;
}

.activity-item i {
  color: #1a237e;
  min-width: 16px;
}

.activity-item small {
  margin-left: auto;
  font-size: 0.8em;
  color: #999;
}

/* Upload Area */
.upload-area {
  border: 2px solid #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  background: white;
  transition: all 0.3s;
}

.upload-area:hover {
  border-color: #1a237e;
  background: #f8f9ff;
}

.upload-area i {
  font-size: 48px;
  color: #ccc;
  margin-bottom: 15px;
}

.upload-area h3 {
  margin-bottom: 10px;
  color: #333;
}

.upload-area p {
  color: #666;
  margin-bottom: 20px;
}

/* Enhanced Payroll Auditor Styles */
.enhanced-payroll-audit-container {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
  margin: 0 auto;
  max-width: 1400px;
}

.audit-header-enhanced {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.audit-header-enhanced h2 {
  font-size: 28px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.audit-header-enhanced .subtitle {
  font-size: 16px;
  opacity: 0.9;
  font-weight: 400;
}

/* Old phase progress styles removed - now using enhanced progress panel phase indicators */

/* Content Switching Container */
.content-switching-container {
  padding: 20px;
  min-height: 500px;
}

.phase-content {
  display: none;
  animation: fadeInUp 0.4s ease-out;
}

.phase-content.active {
  display: block;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* File Selection Enhanced */
.file-selection-enhanced {
  max-width: 1200px;
  margin: 0 auto;
}

.file-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 40px;
}

.file-section {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 25px;
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.file-section:hover {
  border-color: #1a237e;
  box-shadow: 0 4px 15px rgba(26, 35, 126, 0.1);
}

.file-section.current-payroll {
  border-left: 4px solid #28a745;
}

.file-section.previous-payroll {
  border-left: 4px solid #ffc107;
}

.section-header h3 {
  color: #495057;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 18px;
}

.file-upload-area {
  text-align: center;
  padding: 30px;
  border: 2px dashed #dee2e6;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
}

.file-upload-area:hover {
  border-color: #1a237e;
  background: #f8f9ff;
}

.file-upload-area i {
  font-size: 48px;
  color: #6c757d;
  margin-bottom: 15px;
}

.file-upload-area p {
  color: #6c757d;
  margin-bottom: 20px;
  font-size: 16px;
}

/* Audit Configuration */
.audit-config {
  background: white;
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.05);
  border: 1px solid #e9ecef;
}

.config-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.config-section h4 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
}

.date-inputs {
  display: flex;
  gap: 10px;
}

.signature-inputs {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.form-control {
  padding: 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-control:focus {
  outline: none;
  border-color: #1a237e;
  box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
}

.audit-controls {
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #e9ecef;
}

.btn.large {
  padding: 15px 30px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 8px;
  min-width: 250px;
}

.btn.danger {
  background: #dc3545;
  color: white;
}

.btn.danger:hover {
  background: #c82333;
}

/* File Info Styling */
.file-info {
  margin-top: 15px;
  padding: 12px;
  background: #d4edda;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  color: #155724;
  font-size: 14px;
  display: none;
}

.file-info i {
  margin-right: 8px;
  color: #28a745;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .config-grid {
    grid-template-columns: 1fr 1fr;
  }

  .signature-section {
    grid-column: 1 / -1;
  }
}

@media (max-width: 768px) {
  .file-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .config-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .phase-progress {
    flex-wrap: wrap;
    gap: 15px;
  }

  .phase-progress::before {
    display: none;
  }

  .content-switching-container {
    padding: 20px;
  }
}

/* Enhanced Processing Phases */
.processing-phase-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

/* Enhanced Actions - Center the button and processing controls */
.enhanced-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin: 30px 0;
  padding: 20px 0;
  gap: 20px;
}

.processing-controls {
  display: flex;
  gap: 15px;
  align-items: center;
  justify-content: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.process-timer {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #e9ecef;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #495057;
  border: 1px solid #ced4da;
  min-width: 120px;
  justify-content: center;
}

/* Processing logs styling */
.log-entry {
  padding: 4px 8px;
  margin: 2px 0;
  border-radius: 3px;
  font-size: 12px;
  line-height: 1.4;
  background: rgba(0, 0, 0, 0.02);
  border-left: 3px solid #dee2e6;
}

.log-entry.log-error {
  background: rgba(220, 53, 69, 0.1);
  border-left-color: #dc3545;
  color: #721c24;
}

.log-entry.log-success {
  background: rgba(40, 167, 69, 0.1);
  border-left-color: #28a745;
  color: #155724;
}

.log-entry.log-warning {
  background: rgba(255, 193, 7, 0.1);
  border-left-color: #ffc107;
  color: #856404;
}

.log-time {
  color: #6c757d;
  font-weight: normal;
  font-size: 11px;
  margin-right: 8px;
}

.processing-timer {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #e9ecef;
  border-radius: 6px;
  font-family: 'Courier New', monospace;
  font-weight: bold;
  color: #495057;
  border: 1px solid #ced4da;
  min-width: 120px;
  justify-content: center;
}

.processing-timer i {
  color: #6c757d;
}

.phase-header {
  text-align: center;
  margin-bottom: 40px;
}

.phase-header h3 {
  color: #1a237e;
  font-size: 24px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.phase-header p {
  color: #6c757d;
  font-size: 16px;
}

.processing-animation {
  text-align: center;
  margin-bottom: 40px;
}

.large-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1a237e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.processing-status h4 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 18px;
}

.progress-container {
    width: 100%;
    background-color: #e0e0e0;
    border-radius: 5px;
    margin-bottom: 10px;
}

.progress-bar {
    height: 20px;
    background-color: #4CAF50;
    border-radius: 5px;
    text-align: center;
    color: white;
    transition: width 0.3s ease;
}

.progress-bar.completed {
    background-color: #2E7D32; /* Darker green for completed state */
    box-shadow: 0 0 10px rgba(46, 125, 50, 0.5);
}

.success-message-container {
    display: none; /* Hidden by default, set to flex when showing */
    position: relative;
    width: 100%;
    justify-content: center;
    align-items: center;
    padding: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.success-message {
    background-color: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    padding: 25px 30px;
    text-align: center;
    max-width: 500px;
    width: 100%;
    animation: fadeIn 0.5s ease-in-out;
}

.success-message i.fas.fa-check-circle {
    color: #4CAF50;
    font-size: 48px;
    margin-bottom: 15px;
}

.success-message h3 {
    color: #333;
    font-size: 22px;
    margin: 10px 0;
    font-weight: 600;
}

.success-message p {
    color: #555;
    font-size: 16px;
    line-height: 1.5;
    margin: 15px 0;
}

.success-message .btn-primary {
    background-color: #2196F3;
    color: white;
    border: none;
    padding: 10px 20px;
    font-size: 16px;
    cursor: pointer;
    border-radius: 4px;
    transition: background-color 0.3s ease;
    margin-top: 15px;
}

.success-message .btn-primary:hover {
    background-color: #0D8BF0;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.processing-logs {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-top: 30px;
}

.processing-logs h4 {
  color: #495057;
  margin-bottom: 15px;
}

.logs-container {
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 10px;
}

.log-entry {
  padding: 5px 0;
  border-bottom: 1px solid #f8f9fa;
  font-size: 14px;
  color: #495057;
}

.log-entry:last-child {
  border-bottom: none;
}

/* Comparison Stats */
.comparison-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

/* Tracker Stats */
.tracker-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 15px;
  margin-top: 30px;
}

.stat-card {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #1a237e;
  margin-bottom: 5px;
}

.stat-label {
  color: #6c757d;
  font-size: 14px;
}

/* Payroll View Containers */
.payroll-view {
  width: 100%;
  transition: opacity 0.3s ease;
}

.payroll-view.hidden {
  display: none;
}

.processing-content {
  padding: 20px;
}

.processing-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  border-radius: 12px;
}

.processing-header h3 {
  margin: 0 0 10px 0;
  font-size: 1.5em;
}

.processing-header p {
  margin: 0;
  opacity: 0.9;
}

/* Final Report Interface (Enhanced Pre-reporting) */
.final-report-interface,
.pre-reporting-interface {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.final-report-header,
.pre-reporting-header {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 25px;
  border-radius: 12px;
  margin-bottom: 25px;
  text-align: center;
}

.final-report-header h3,
.pre-reporting-header h3 {
  margin: 0 0 10px 0;
  font-size: 1.6em;
  font-weight: 600;
}

.interface-subtitle {
  margin: 0;
  font-size: 1.0em;
  opacity: 0.9;
  font-weight: 300;
}

/* Shared Report Configuration Panel */
.shared-report-configuration {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  padding: 20px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
}

.config-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  align-items: end;
}

.config-field {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.config-field label {
  font-size: 0.9em;
  font-weight: 500;
  color: white;
  opacity: 0.9;
}

.config-field input,
.config-field select {
  padding: 8px 12px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  font-size: 14px;
  transition: all 0.3s ease;
}

.config-field input:focus,
.config-field select:focus {
  outline: none;
  border-color: #ffffff;
  background: #ffffff;
  box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.2);
}

/* Enhanced Sorting Controls */
.enhanced-sorting {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-right: 15px;
}

.enhanced-sorting label {
  font-size: 0.9em;
  font-weight: 500;
  color: #495057;
}

.enhanced-sorting select {
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 150px;
  transition: all 0.3s ease;
}

.enhanced-sorting select:focus {
  outline: none;
  border-color: #1a237e;
  box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
}

.priority-filter {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-right: 15px;
}

.priority-filter label {
  font-size: 0.9em;
  font-weight: 500;
  color: #495057;
}

.priority-filter select {
  padding: 8px 12px;
  border: 2px solid #e9ecef;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 140px;
  transition: all 0.3s ease;
}

.priority-filter select:focus {
  outline: none;
  border-color: #1a237e;
  box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
}

/* Change Flag Groups */
.change-flag-groups {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.change-flag-group {
  background: white;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  overflow: hidden;
}

.flag-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.3s ease;
}

.flag-header:hover {
  background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

.flag-header h4 {
  margin: 0;
  color: #495057;
  font-size: 1.1em;
}

.flag-changes {
  padding: 15px;
  background: #fafbfc;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: #6c757d;
}

/* Smart Report Preview */
.smart-report-preview {
  background: white;
  border-radius: 12px;
  padding: 30px;
  margin: 20px 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid #e9ecef;
}

.preview-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #f8f9fa;
}

.preview-header h3 {
  color: #1a237e;
  margin: 0 0 10px 0;
  font-size: 1.5em;
}

.preview-header p {
  color: #6c757d;
  margin: 0;
  font-style: italic;
}

.preview-content {
  display: grid;
  gap: 25px;
  margin-bottom: 30px;
}

.executive-summary {
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #1a237e;
}

.executive-summary h4 {
  color: #1a237e;
  margin: 0 0 15px 0;
  font-size: 1.2em;
}

.key-metrics {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin-bottom: 15px;
}

.metric {
  background: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.9em;
  font-weight: 500;
  color: #495057;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.summary-text p {
  margin: 8px 0;
  line-height: 1.6;
  color: #495057;
}

.sections-preview,
.recommendations-preview {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
}

.sections-preview h4,
.recommendations-preview h4 {
  color: #495057;
  margin: 0 0 15px 0;
  font-size: 1.1em;
}

.sections-preview ul,
.recommendations-preview ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sections-preview li,
.recommendations-preview li {
  padding: 8px 0;
  border-bottom: 1px solid #e9ecef;
}

.sections-preview li:last-child,
.recommendations-preview li:last-child {
  border-bottom: none;
}

.priority-high {
  background: #dc3545;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
  margin-right: 8px;
}

.priority-moderate {
  background: #ffc107;
  color: #212529;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
  margin-right: 8px;
}

.priority-low {
  background: #28a745;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
  margin-right: 8px;
}

.preview-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding-top: 20px;
  border-top: 2px solid #f8f9fa;
}

.preview-actions .btn {
  min-width: 180px;
}

.summary-stats {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
}

.stat-item strong {
  font-size: 1.8em;
  font-weight: bold;
}

.bulk-categories {
  display: flex;
  flex-direction: column;
  gap: 20px;
  margin-bottom: 30px;
}

.bulk-category {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.category-header {
  background: #f8f9fa;
  padding: 20px;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.category-header h4 {
  margin: 0;
  color: #1a237e;
  display: flex;
  align-items: center;
  gap: 10px;
}

.category-stats {
  display: flex;
  gap: 15px;
  font-size: 0.9em;
  color: #6c757d;
}

.changes-list {
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.change-item {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 15px;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  margin-bottom: 10px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.change-item:hover {
  background: #f8f9fa;
  border-color: #1a237e;
}

.change-item.selected {
  background: #e3f2fd;
  border-color: #1a237e;
  box-shadow: 0 2px 8px rgba(26, 35, 126, 0.1);
}

.change-checkbox {
  margin-top: 2px;
}

.change-checkbox input[type="checkbox"] {
  width: 18px;
  height: 18px;
  cursor: pointer;
}

.change-details {
  flex: 1;
}

.change-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.employee-info {
  font-weight: 600;
  color: #333;
}

.priority-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
  text-transform: uppercase;
}

.priority-high {
  background: #ffebee;
  color: #c62828;
}

.priority-moderate {
  background: #fff3e0;
  color: #ef6c00;
}

.priority-low {
  background: #e8f5e8;
  color: #2e7d32;
}

.change-description {
  margin-bottom: 5px;
  color: #495057;
}

.change-type {
  background: #e9ecef;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 0.8em;
  margin-left: 10px;
}

.change-values {
  font-size: 0.9em;
  color: #6c757d;
}

.more-changes {
  text-align: center;
  padding: 15px;
}

.btn-link {
  background: none;
  border: none;
  color: #1a237e;
  text-decoration: underline;
  cursor: pointer;
  font-size: 0.9em;
}

.btn-link:hover {
  color: #3f51b5;
}

.pre-reporting-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  padding: 25px;
  background: #f8f9fa;
  border-radius: 12px;
}

.pre-reporting-error {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.pre-reporting-error h3 {
  color: #dc3545;
  margin-bottom: 15px;
}

.pre-reporting-success {
  text-align: center;
  padding: 60px 20px;
  color: #28a745;
}

.success-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.pre-reporting-success h3 {
  color: #28a745;
  margin-bottom: 15px;
}

.success-actions {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-top: 25px;
}

.error-message {
  margin-bottom: 20px;
}

.alert {
  padding: 15px;
  border-radius: 8px;
  position: relative;
}

.alert-danger {
  background: #f8d7da;
  border: 1px solid #f5c6cb;
  color: #721c24;
}

.alert .close {
  position: absolute;
  top: 10px;
  right: 15px;
  background: none;
  border: none;
  font-size: 20px;
  cursor: pointer;
  color: inherit;
}

.changes-summary {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
}

.changes-container {
  max-height: 400px;
  overflow-y: auto;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  padding: 15px;
}

.loading-changes {
  text-align: center;
  padding: 40px;
  color: #6c757d;
}

.loading-changes i {
  font-size: 24px;
  margin-bottom: 10px;
}

.selection-controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  padding: 20px;
}

/* Report Generation */
.report-generation-status {
  text-align: center;
  margin-bottom: 40px;
}

.report-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.report-types {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.report-type {
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.report-type i {
  font-size: 32px;
  color: #1a237e;
}

.report-type span {
  font-weight: 600;
  color: #495057;
}

.report-status {
  padding: 5px 10px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  background: #ffc107;
  color: #856404;
}

/* Processing Results */
.processing-results {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

/* Comparison Styles */
.file-selection-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.file-selection-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.file-selection-card h3 {
  margin-bottom: 15px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.file-input-area {
  text-align: center;
  padding: 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  transition: all 0.3s;
}

.file-input-area:hover {
  border-color: #1a237e;
  background: #f8f9ff;
}

.file-info {
  margin-top: 10px;
  padding: 10px;
  background: #e8f5e8;
  border-radius: 4px;
  color: #2e7d32;
  font-size: 14px;
}

.comparison-actions {
  text-align: center;
  margin: 20px 0;
}

.comparison-results {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.comparison-summary {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  margin-bottom: 20px;
}

.comparison-details {
  max-height: 400px;
  overflow-y: auto;
}

.comparison-item {
  border: 1px solid #e9ecef;
  border-radius: 6px;
  margin-bottom: 10px;
  padding: 15px;
}

.comparison-item.changed {
  border-left: 4px solid #ffc107;
  background: #fffbf0;
}

.comparison-item.added {
  border-left: 4px solid #28a745;
  background: #f8fff9;
}

.comparison-item.removed {
  border-left: 4px solid #dc3545;
  background: #fff8f8;
}

/* Status Bar */
.status-bar {
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  padding: 10px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
  color: #666;
}

/* Notification Animations */
@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOut {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* Enhanced Loans Section Styles */
.loans-actions {
  display: flex;
  gap: 10px;
  align-items: center;
}

.classification-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin: 20px 0;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  transition: transform 0.2s;
}

.summary-card:hover {
  transform: translateY(-2px);
}

.summary-card.in-house {
  border-left: 4px solid #28a745;
}

.summary-card.external {
  border-left: 4px solid #007bff;
}

.summary-card.unclassified {
  border-left: 4px solid #ffc107;
}

.summary-icon {
  font-size: 24px;
  color: #666;
}

.summary-count {
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.summary-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.loan-types-container {
  margin-top: 20px;
}

.loan-types-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-bottom: 10px;
  border-bottom: 2px solid #e9ecef;
}

.view-controls {
  display: flex;
  gap: 5px;
}

.btn-icon {
  background: none;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  cursor: pointer;
  color: #666;
  transition: all 0.3s;
}

.btn-icon:hover {
  background: #f8f9fa;
  color: #1a237e;
  border-color: #1a237e;
}

.loan-types-tree {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  background: white;
}

.loan-type-group {
  border-bottom: 1px solid #e9ecef;
}

.loan-type-group:last-child {
  border-bottom: none;
}

.loan-type-header {
  display: flex;
  align-items: center;
  padding: 15px 20px;
  background: #f8f9fa;
  cursor: pointer;
  transition: background 0.3s;
}

.loan-type-header:hover {
  background: #e9ecef;
}

.loan-type-header.expanded {
  background: #e3f2fd;
}

.loan-type-toggle {
  margin-right: 10px;
  font-size: 12px;
  color: #666;
  transition: transform 0.3s;
}

.loan-type-toggle.expanded {
  transform: rotate(90deg);
}

.loan-type-info {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 15px;
}

.loan-type-name {
  font-weight: bold;
  color: #333;
}

.loan-type-classification {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: bold;
  text-transform: uppercase;
}

.loan-type-classification.in-house {
  background: #d4edda;
  color: #155724;
}

.loan-type-classification.external {
  background: #cce5ff;
  color: #004085;
}

.loan-type-count {
  color: #666;
  font-size: 12px;
}

.loan-type-actions {
  display: flex;
  gap: 5px;
}

.loan-type-items {
  display: none;
  padding: 0;
}

.loan-type-items.expanded {
  display: block;
}

.loan-item {
  display: flex;
  align-items: center;
  padding: 12px 20px 12px 50px;
  border-bottom: 1px solid #f1f3f4;
  transition: background 0.3s;
}

.loan-item:hover {
  background: #f8f9fa;
}

.loan-item:last-child {
  border-bottom: none;
}

.loan-item-icon {
  margin-right: 10px;
  color: #666;
  font-size: 14px;
}

.loan-item-name {
  flex: 1;
  color: #333;
}

.loan-item-column {
  color: #666;
  font-size: 12px;
  margin-left: 10px;
}

.loan-item-actions {
  display: flex;
  gap: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}

.loan-item:hover .loan-item-actions {
  opacity: 1;
}

.column-headers-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 2px solid #e9ecef;
}

.column-headers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.column-header-card {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  text-align: center;
}

.column-header-name {
  font-weight: bold;
  color: #333;
  margin-bottom: 5px;
}

.column-header-type {
  color: #666;
  font-size: 12px;
}

.column-header-fixed {
  background: #fff3cd;
  border-color: #ffeaa7;
}

/* Auto-detected items in modal */
.auto-detected-items {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
}

.auto-detected-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-bottom: 1px solid #f1f3f4;
}

.auto-detected-item:last-child {
  border-bottom: none;
}

.auto-detected-item input[type="checkbox"] {
  margin-right: 10px;
}

.loan-type-items-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e9ecef;
  border-radius: 4px;
}

.loan-type-item-entry {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  border-bottom: 1px solid #f1f3f4;
}

.loan-type-item-entry:last-child {
  border-bottom: none;
}

/* Classification Report Modal */
.large-modal .modal-content {
  max-width: 900px;
  width: 90%;
}

.classification-report-content {
  max-height: 600px;
  overflow-y: auto;
}

.report-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.report-card {
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.report-card h4 {
  background: #f8f9fa;
  margin: 0;
  padding: 15px;
  border-bottom: 1px solid #e9ecef;
  text-align: center;
}

.report-content {
  padding: 15px;
  max-height: 400px;
  overflow-y: auto;
}

.report-loan-type {
  margin-bottom: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 4px;
}

.report-loan-name {
  font-weight: bold;
  margin-bottom: 5px;
}

.report-loan-items {
  font-size: 12px;
  color: #666;
  margin-left: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .main-nav {
    overflow-x: auto;
  }

  .nav-tab {
    white-space: nowrap;
  }

  .classification-summary {
    grid-template-columns: 1fr;
  }

  .loans-actions {
    flex-direction: column;
    align-items: stretch;
  }

  .loan-types-header {
    flex-direction: column;
    gap: 10px;
    align-items: stretch;
  }

  .report-summary {
    grid-template-columns: 1fr;
  }

  /* Header responsive adjustments */
  .header-content {
    flex-direction: column;
    gap: 10px;
    align-items: center;
  }

  .logo-section {
    text-align: center;
  }

  .header-subtitle {
    align-items: center;
    text-align: center;
  }

  .logo-section h1 {
    font-size: 20px;
  }

  .header-created-by,
  .header-rights {
    font-size: 10px;
  }
}

/* Dictionary Manager Styles */
.dictionary-header {
  margin-bottom: 20px;
}

.dictionary-header h2 {
  margin-bottom: 10px;
  color: #333;
}

.dictionary-actions {
  display: flex;
  gap: 10px;
  margin-top: 15px;
}

.dictionary-tabs {
  display: flex;
  background: #f8f9fa;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
  margin-bottom: 0;
}

.dict-tab {
  background: none;
  border: none;
  padding: 15px 20px;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  border-bottom: 3px solid transparent;
  transition: all 0.3s;
  flex: 1;
  justify-content: center;
}

.dict-tab:hover {
  background: #e9ecef;
  color: #1a237e;
}

.dict-tab.active {
  background: white;
  color: #1a237e;
  border-bottom-color: #1a237e;
}

.dictionary-content {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 0 0 8px 8px;
  padding: 20px;
}

.dict-section-content {
  display: none;
}

.dict-section-content.active {
  display: block;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e9ecef;
}

.section-header h3 {
  margin: 0;
  color: #333;
}

.items-table-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background: white;
}

.items-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  margin-top: 10px;
  border-radius: 8px;
  overflow: hidden;
}

.items-table th,
.items-table td {
  padding: 14px 12px;
  text-align: left;
  border-bottom: 1px solid #f1f3f4;
}

.items-table th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid #dee2e6;
}

.items-table tr:hover {
  background: #f8f9ff;
}

.items-table tr:last-child td {
  border-bottom: none;
}

/* Dictionary table styling for consistency */
.dictionary-table {
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.dictionary-table th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 2px solid #dee2e6;
  font-weight: 600;
  color: #2c3e50;
  padding: 16px 12px;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.dictionary-table td {
  padding: 14px 12px;
  border-bottom: 1px solid #f1f3f4;
  background-color: white;
  vertical-align: middle;
}

.dictionary-table tr:hover td {
  background-color: #f8f9ff;
}

.dictionary-table tr:last-child td {
  border-bottom: none;
}

.table-container {
  overflow-x: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  background: white;
  margin-top: 16px;
}

.item-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.edit-button,
.delete-button {
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  font-weight: 500;
  transition: all 0.2s ease;
  min-width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.edit-button {
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
}

.edit-button:hover {
  background: #1976d2;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(25, 118, 210, 0.3);
}

.delete-button {
  background: #ffebee;
  color: #d32f2f;
  border: 1px solid #ffcdd2;
}

.delete-button:hover {
  background: #d32f2f;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(211, 47, 47, 0.3);
}

/* Modal Styles */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-modal {
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.close-modal:hover {
  color: #333;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  padding: 20px;
  border-top: 1px solid #e9ecef;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #333;
}

.form-input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #1a237e;
  box-shadow: 0 0 0 2px rgba(26, 35, 126, 0.1);
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
}

/* Toggle Switch Styles */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.4s;
  border-radius: 50%;
}

.toggle-input:checked + .toggle-slider {
  background-color: #1a237e;
}

.toggle-input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Utility Classes */
.hidden {
  display: none !important;
}

.text-center {
  text-align: center;
}

.text-success {
  color: #28a745;
}

.text-error {
  color: #dc3545;
}

.text-warning {
  color: #ffc107;
}

/* Loading Animation */
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #1a237e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Processing Indicator Styles */
.processing-indicator {
  text-align: center;
  padding: 40px 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 2px dashed #dee2e6;
}

.processing-indicator .loading {
  width: 40px;
  height: 40px;
  border-width: 4px;
  margin-bottom: 20px;
}

.processing-indicator h4 {
  color: #1a237e;
  margin-bottom: 10px;
  font-size: 18px;
}

.processing-indicator p {
  color: #666;
  margin-bottom: 30px;
  font-size: 14px;
}

.processing-steps {
  max-width: 400px;
  margin: 0 auto;
  text-align: left;
}

.processing-steps .step {
  padding: 10px 15px;
  margin-bottom: 8px;
  background: #e9ecef;
  border-radius: 6px;
  color: #666;
  font-size: 14px;
  transition: all 0.3s ease;
}

.processing-steps .step.active {
  background: #1a237e;
  color: white;
  font-weight: 500;
  transform: translateX(5px);
}

.processing-steps .step.completed {
  background: #28a745;
  color: white;
}

/* Dynamic Processing View Styles */
.processing-view {
  min-height: 80vh;
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  border: 1px solid #e9ecef;
}

.processing-header {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 30px;
  text-align: center;
}

.processing-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  margin-bottom: 20px;
}

.processing-title i {
  font-size: 32px;
  animation: pulse 2s infinite;
}

.processing-title h2 {
  font-size: 28px;
  font-weight: 600;
  margin: 0;
}

.processing-info {
  background: rgba(255,255,255,0.1);
  border-radius: 8px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.audit-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  text-align: center;
}

.detail-item .label {
  display: block;
  font-size: 14px;
  opacity: 0.8;
  margin-bottom: 5px;
}

.detail-item .value {
  display: block;
  font-size: 16px;
  font-weight: 600;
}

.processing-main {
  padding: 40px;
}

.processing-animation {
  text-align: center;
  margin-bottom: 40px;
}

.large-spinner {
  width: 80px;
  height: 80px;
  border: 6px solid #f3f3f3;
  border-top: 6px solid #1a237e;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 30px;
}

.processing-animation h3 {
  color: #1a237e;
  font-size: 24px;
  margin-bottom: 10px;
}

.processing-animation p {
  color: #666;
  font-size: 16px;
}

.processing-progress {
  margin-bottom: 40px;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #e9ecef;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1a237e 0%, #3f51b5 100%);
  border-radius: 6px;
  transition: width 0.5s ease;
  width: 0%;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
  color: #666;
}

.progress-text #progress-percentage {
  font-weight: 600;
  color: #1a237e;
}

.processing-steps-detailed {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.step-item {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: all 0.3s ease;
  border-left: 4px solid #e9ecef;
}

.step-item.active {
  border-left-color: #1a237e;
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(26, 35, 126, 0.2);
}

.step-item.completed {
  border-left-color: #28a745;
  background: #f8fff9;
}

.step-item.error {
  border-left-color: #dc3545;
  background: #fff8f8;
}

.step-icon {
  text-align: center;
  margin-bottom: 15px;
}

.step-icon i {
  font-size: 24px;
  color: #666;
  transition: all 0.3s ease;
}

.step-item.active .step-icon i {
  color: #1a237e;
}

.step-item.completed .step-icon i {
  color: #28a745;
}

.step-item.error .step-icon i {
  color: #dc3545;
}

.step-content h4 {
  color: #333;
  font-size: 16px;
  margin-bottom: 8px;
}

.step-content p {
  color: #666;
  font-size: 14px;
  margin-bottom: 10px;
}

.step-status {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  color: #666;
}

.step-item.active .step-status {
  color: #1a237e;
}

.step-item.completed .step-status {
  color: #28a745;
}

.step-item.error .step-status {
  color: #dc3545;
}

.processing-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  color: #1a237e;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Payroll Audit Styles */
.payroll-audit-container {
  padding: 20px;
}

.audit-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
}

.audit-section {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.section-header.current-payroll {
  background: #4CAF50;
}

.section-header.previous-payroll {
  background: #2196F3;
}

.section-header.signature-header {
  background: #FF9800;
}

.section-content {
  padding: 20px;
}

.file-selection {
  margin-bottom: 20px;
}

.file-selection label {
  display: block;
  margin-bottom: 10px;
  font-weight: 500;
}

.file-upload-area {
  border: 2px dashed #ddd;
  border-radius: 8px;
  padding: 20px;
  text-align: center;
  background: #f8f9fa;
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 10px;
}

.file-upload-area:hover {
  border-color: #1a237e;
  background: #f0f0f0;
}

.file-upload-area.dragover {
  border-color: #4CAF50;
  background: #e8f5e8;
}

.file-upload-area i {
  font-size: 24px;
  color: #666;
  margin-bottom: 10px;
  display: block;
}

.file-upload-area p {
  margin: 10px 0;
  color: #666;
  font-size: 14px;
}

.file-info {
  margin-top: 10px;
  padding: 10px;
  background: #e8f5e8;
  border-radius: 4px;
  display: none;
}

.file-info i {
  color: #4CAF50;
  margin-right: 8px;
}

.date-selection {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.date-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

.form-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.signature-section {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.signature-content {
  padding: 20px;
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  align-items: start;
}

.signature-field label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

/* Old signature field styles removed - configuration moved to Final Report Interface */

/* Old signature field styles removed - configuration moved to Final Report Interface */

.audit-actions {
  text-align: center;
  margin-bottom: 30px;
}

.audit-requirements {
  margin-top: 10px;
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.btn.large {
  padding: 15px 40px;
  font-size: 16px;
  font-weight: bold;
}

.btn:disabled {
  background-color: #ccc !important;
  color: #666 !important;
  cursor: not-allowed !important;
  opacity: 0.6;
}

.btn:disabled:hover {
  background-color: #ccc !important;
  transform: none !important;
}

.audit-results {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.audit-summary {
  margin-bottom: 30px;
}

.summary-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.summary-item {
  background: #f8f9fa;
  padding: 15px;
  border-radius: 6px;
  text-align: center;
}

.summary-label {
  display: block;
  font-size: 12px;
  color: #666;
  margin-bottom: 5px;
}

.summary-value {
  display: block;
  font-size: 24px;
  font-weight: bold;
  color: #1a237e;
}

.report-files {
  margin-top: 30px;
}

.report-links {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 10px;
  margin-top: 15px;
}

.report-link {
  display: block;
  padding: 10px 15px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 6px;
  text-decoration: none;
  color: #333;
  text-align: center;
  transition: all 0.3s ease;
}

.report-link:hover {
  background: #e9ecef;
  border-color: #1a237e;
  color: #1a237e;
}

.report-link i {
  display: block;
  font-size: 20px;
  margin-bottom: 5px;
}

/* Auto-Learning Styles */
.auto-learning-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.auto-learning-header h3 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.auto-learning-controls {
  display: flex;
  gap: 10px;
}

.auto-learning-controls .btn {
  padding: 8px 16px;
  font-size: 0.9rem;
}

.auto-learning-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.auto-learning-stats .stat-card {
  background: white;
  padding: 20px;
  border-radius: 12px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  text-align: center;
  border-left: 4px solid #1976d2;
}

.auto-learning-stats .stat-value {
  font-size: 2rem;
  font-weight: bold;
  color: #1976d2;
  margin-bottom: 5px;
}

.auto-learning-stats .stat-label {
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.pending-items-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.pending-items-container h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
}

.pending-items-table-container {
  overflow: auto;
  max-height: 400px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: white;
}

.pending-items-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0;
}

.pending-items-table th,
.pending-items-table td {
  padding: 12px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  white-space: nowrap;
}

.pending-items-table th {
  background-color: #f8f9fa;
  font-weight: 600;
  color: #333;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid #dee2e6;
}

.pending-items-table tbody tr:hover {
  background-color: #f8f9ff;
}

.pending-items-table tbody {
  background: white;
}

.confidence-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.confidence-badge.high {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.confidence-badge.medium {
  background-color: #fff3e0;
  color: #f57c00;
}

.confidence-badge.low {
  background-color: #ffebee;
  color: #d32f2f;
}

/* Item Status Tags */
.item-tags {
  display: inline-flex;
  gap: 4px;
  margin-left: 8px;
  align-items: center;
}

.item-tag {
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  white-space: nowrap;
}

.item-tag.auto-approved {
  background-color: #e8f5e8;
  color: #2e7d32;
  border: 1px solid #4caf50;
  box-shadow: 0 1px 3px rgba(76, 175, 80, 0.2);
}

.item-tag.fixed-item {
  background-color: #fce4ec;
  color: #c2185b;
  border: 1px solid #e91e63;
  box-shadow: 0 1px 3px rgba(233, 30, 99, 0.2);
}

.item-name-container {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 4px;
}

.section-select {
  padding: 6px 10px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 0.9rem;
  min-width: 150px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 0.8rem;
  margin: 0 2px;
}

.no-items {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px;
}

.activity-log-container {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.activity-log-container h4 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 1.2rem;
}

.activity-log {
  max-height: 300px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.activity-icon i {
  color: #666;
  font-size: 0.9rem;
}

.activity-content {
  flex: 1;
}

.activity-message {
  color: #333;
  font-size: 0.9rem;
  margin-bottom: 4px;
}

.activity-time {
  color: #999;
  font-size: 0.8rem;
}

.no-activity {
  text-align: center;
  color: #666;
  font-style: italic;
  padding: 40px;
}

/* Modal Base Styles - All modals centered by default */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 10px 30px rgba(0,0,0,0.3);
  position: relative;
  margin: auto;
}

/* Edit Modal - Also centered now */
#item-edit-modal {
  align-items: center;
  justify-content: center;
}

#item-edit-modal .modal-content {
  max-width: 600px;
  width: 90%;
  margin: auto;
}

/* Settings and Help Modal - Centered */
#settings-modal,
#help-modal {
  align-items: center;
  justify-content: center;
}

#settings-modal .modal-content,
#help-modal .modal-content {
  max-width: 800px;
  width: 90%;
  margin: auto;
}

/* Section Selection Modal - Centered */
#section-selection-modal {
  align-items: center;
  justify-content: center;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  border-radius: 12px 12px 0 0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.close-modal {
  font-size: 1.5rem;
  cursor: pointer;
  color: #666;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.close-modal:hover {
  background-color: #e0e0e0;
  color: #333;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 20px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
  background-color: #f8f9fa;
  border-radius: 0 0 12px 12px;
}

/* Responsive Design Updates */
@media (max-width: 768px) {
  .auto-learning-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .auto-learning-controls {
    flex-direction: column;
    gap: 5px;
  }

  .auto-learning-controls .btn {
    font-size: 0.8rem;
    padding: 6px 12px;
  }

  /* Responsive signature section */
  .signature-content {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .signature-field.name-field {
    grid-column: 1;
    text-align: center;
    order: 1;
  }

  .signature-field.designation-field {
    grid-column: 1;
    text-align: center;
    order: 2;
  }

  .signature-field.report-type-field {
    grid-column: 1;
    text-align: center;
    order: 3; /* Report type last on mobile */
  }
}

@media (max-width: 480px) {
  .signature-field.report-type-field select {
    font-size: 13px;
    padding: 10px;
  }
}

/* Hybrid Extraction Results Styles */
.extraction-metrics {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  margin: 20px 0;
}

.metric-card {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 15px;
  text-align: center;
  border-left: 4px solid #007bff;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #007bff;
  margin-bottom: 5px;
}

.metric-label {
  font-size: 12px;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Next steps styling */
.next-steps {
  background: #e7f3ff;
  border-radius: 6px;
  padding: 15px;
  margin-top: 20px;
  border-left: 4px solid #007bff;
}

.next-steps h5 {
  margin-top: 0;
  color: #007bff;
}

.next-steps ol {
  margin-bottom: 0;
}

.next-steps li {
  margin-bottom: 8px;
}

/* Responsive metrics */
@media (max-width: 768px) {
  .extraction-metrics {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .extraction-metrics {
    grid-template-columns: 1fr;
  }
}

/* Dictionary Manager Styles for Data Builder */
.dictionary-manager-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  overflow: hidden;
}

/* Section Tabs */
.section-tabs {
  display: flex;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  overflow-x: auto;
}

.section-tab {
  background: none;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 8px;
}

.section-tab:hover {
  background: #e9ecef;
  color: #495057;
}

.section-tab.active {
  background: white;
  color: #007bff;
  border-bottom-color: #007bff;
}

.section-tab i {
  font-size: 16px;
}

/* Dictionary Controls */
.dictionary-controls {
  padding: 15px;
  background: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  display: flex;
  align-items: center;
  gap: 5px;
  font-weight: 500;
  color: #495057;
  margin: 0;
}

/* Section Content */
.section-content {
  position: relative;
}

.section-panel {
  display: none;
  padding: 20px;
}

.section-panel.active {
  display: block;
}

.section-header {
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #007bff;
}

.section-header h4 {
  margin: 0;
  color: #343a40;
  font-size: 18px;
  font-weight: 600;
}

/* Dictionary Table */
.dictionary-table-wrapper {
  overflow-x: auto;
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid #dee2e6;
  border-radius: 6px;
}

.dictionary-table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background: white;
}

.dictionary-table th {
  background: #343a40;
  color: white;
  padding: 12px 15px;
  text-align: left;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
  border-bottom: 2px solid #495057;
}

.dictionary-table td {
  padding: 12px 15px;
  border-bottom: 1px solid #dee2e6;
  vertical-align: middle;
}

.dictionary-table tbody tr:hover {
  background-color: #f8f9fa;
}

.dictionary-table tbody tr:nth-child(even) {
  background-color: #fafafa;
}

.dictionary-table tbody tr:nth-child(even):hover {
  background-color: #f0f0f0;
}

/* Format and Value Format Styling */
.dictionary-table .format-cell {
  font-family: 'Courier New', monospace;
  background: #f8f9fa;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #495057;
}

.dictionary-table .value-format-cell {
  color: #6c757d;
  font-style: italic;
}

/* Include in Report Toggle */
.include-toggle {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.include-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: .4s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.include-toggle input:checked + .toggle-slider {
  background-color: #007bff;
}

.include-toggle input:checked + .toggle-slider:before {
  transform: translateX(26px);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 5px;
}

.action-btn {
  background: none;
  border: none;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.action-btn.edit {
  color: #007bff;
}

.action-btn.edit:hover {
  background: #e3f2fd;
}

.action-btn.delete {
  color: #dc3545;
}

.action-btn.delete:hover {
  background: #ffebee;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.empty-state i {
  font-size: 48px;
  margin-bottom: 15px;
  opacity: 0.5;
}

.empty-state h4 {
  margin: 0 0 10px 0;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* Column Preview Modal */
.column-preview-list {
  max-height: 400px;
  overflow-y: auto;
}

.section-group {
  margin-bottom: 20px;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  overflow: hidden;
}

.section-title {
  background: #f8f9fa;
  color: #495057;
  padding: 10px 15px;
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #e9ecef;
}

.section-items {
  padding: 10px;
}

.column-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  margin-bottom: 5px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 13px;
}

.column-item:last-child {
  margin-bottom: 0;
}

.column-item i {
  color: #28a745;
  font-size: 12px;
}

.column-label {
  font-weight: 500;
  color: #495057;
}

/* Dictionary Edit/Add/Delete Modals */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  padding: 20px;
  border-bottom: 1px solid #e9ecef;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #343a40;
  font-size: 18px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.close-modal {
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  line-height: 1;
}

.close-modal:hover {
  color: #dc3545;
}

.modal-body {
  padding: 20px;
}

.modal-footer {
  padding: 15px 20px;
  border-top: 1px solid #e9ecef;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-group label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #495057;
}

.form-group input,
.form-group select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #007bff;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.form-group input[type="checkbox"] {
  width: auto;
  margin-right: 8px;
}

.text-warning {
  color: #856404;
  background: #fff3cd;
  padding: 10px;
  border-radius: 4px;
  border: 1px solid #ffeaa7;
}

/* Real-time extraction effects */
.new-item-highlight {
  background: linear-gradient(90deg, #fff3cd, #ffffff) !important;
  border-left: 4px solid #ffc107 !important;
  animation: newItemPulse 2s ease-in-out;
}

@keyframes newItemPulse {
  0% {
    background: #fff3cd;
    transform: scale(1);
  }
  50% {
    background: #ffeaa7;
    transform: scale(1.02);
  }
  100% {
    background: #ffffff;
    transform: scale(1);
  }
}

.real-time-activity {
  border-left: 3px solid #007bff;
  background: #f8f9fa;
  animation: slideInFromRight 0.3s ease-out;
}

@keyframes slideInFromRight {
  0% {
    transform: translateX(100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Extraction in progress indicators */
.extraction-in-progress {
  position: relative;
}

.extraction-in-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #007bff, #28a745, #007bff);
  background-size: 200% 100%;
  animation: progressBar 2s linear infinite;
}

@keyframes progressBar {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Real-time stats updates */
.stat-updating {
  animation: statPulse 0.5s ease-in-out;
}

@keyframes statPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); color: #007bff; }
  100% { transform: scale(1); }
}

/* Settings and Help Modal Styles */
.settings-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.settings-section h4 {
  color: #495057;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-item {
  margin-bottom: 15px;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  margin: 0;
}

.setting-item input[type="number"] {
  width: 80px;
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-left: 8px;
}

.setting-item small {
  display: block;
  color: #666;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 24px;
}

.help-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.help-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.help-section h4 {
  color: #495057;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.help-section ul,
.help-section ol {
  margin-left: 20px;
}

.help-section li {
  margin-bottom: 8px;
  line-height: 1.5;
}

.help-section p {
  line-height: 1.6;
  color: #555;
}

/* Header button styles */
.header-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 18px;
  padding: 8px;
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-btn:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

.header-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Modal Animation */
@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes modalSlideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

#item-edit-modal .modal-content {
  animation: modalSlideInLeft 0.3s ease-out;
}

/* App Footer Styles */
.app-footer {
  background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  color: white;
  margin-top: auto;
  border-top: 3px solid #3498db;
  flex-shrink: 0; /* Prevent footer from shrinking */
}

.footer-content {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 20px;
  padding: 20px 30px;
  max-width: 1400px;
  margin: 0 auto;
  align-items: center;
}

.footer-left {
  text-align: left;
}

.footer-center {
  text-align: center;
}

.footer-right {
  text-align: right;
}

.footer-app-name {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 5px;
}

.footer-app-name i {
  font-size: 18px;
}

.footer-version {
  font-size: 12px;
  color: #bdc3c7;
}

.footer-copyright {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 5px;
  color: #ecf0f1;
}

.footer-rights {
  font-size: 12px;
  color: #bdc3c7;
}

.footer-tech {
  font-size: 13px;
  font-weight: 500;
  margin-bottom: 5px;
  color: #ecf0f1;
}

.footer-contact {
  font-size: 11px;
  color: #bdc3c7;
}

/* Status Bar within Footer */
.app-footer .status-bar {
  background: rgba(0, 0, 0, 0.2);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding: 8px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.status-left {
  color: #ecf0f1;
}

.status-right {
  color: #3498db;
}

/* Responsive Footer */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
    gap: 15px;
    padding: 15px 20px;
  }

  .footer-left,
  .footer-right {
    text-align: center;
  }

  .footer-app-name {
    justify-content: center;
  }

  .app-footer .status-bar {
    padding: 8px 20px;
    flex-direction: column;
    gap: 5px;
  }
}

/* Performance Settings Styles */
.performance-info {
  margin-top: 10px;
  padding: 10px;
  background: #f0f8ff;
  border-radius: 6px;
  border-left: 4px solid #007bff;
}

.performance-benefit {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
  font-size: 13px;
  color: #495057;
}

.performance-benefit:last-child {
  margin-bottom: 0;
}

.performance-benefit i {
  font-size: 12px;
  min-width: 16px;
}

.text-success {
  color: #28a745 !important;
}

.text-info {
  color: #17a2b8 !important;
}

/* Settings Modal Enhancements */
.settings-section {
  margin-bottom: 25px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e9ecef;
}

.settings-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.settings-section h4 {
  color: #495057;
  margin-bottom: 15px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
}

.setting-item {
  margin-bottom: 15px;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  color: #495057;
  cursor: pointer;
}

.setting-item input[type="checkbox"] {
  margin: 0;
  transform: scale(1.1);
}

.setting-item input[type="number"] {
  padding: 6px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  width: 80px;
  margin-left: 8px;
}

.setting-item small {
  display: block;
  color: #6c757d;
  font-size: 12px;
  margin-top: 4px;
  margin-left: 24px;
}

/* Data Management Buttons */
.setting-item .btn {
  margin-bottom: 8px;
  min-width: 150px;
}

.setting-item .btn.danger {
  background-color: #dc3545;
  border-color: #dc3545;
  color: white;
}

.setting-item .btn.danger:hover {
  background-color: #c82333;
  border-color: #bd2130;
}

.setting-item .btn.secondary {
  background-color: #6c757d;
  border-color: #6c757d;
  color: white;
}

.setting-item .btn.secondary:hover {
  background-color: #5a6268;
  border-color: #545b62;
}

/* Custom Confirmation Dialog */
.form-control {
  display: block;
  width: 100%;
  padding: 8px 12px;
  font-size: 14px;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 4px;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: #80bdff;
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.text-danger {
  color: #dc3545 !important;
}

.text-muted {
  color: #6c757d !important;
  font-size: 12px;
  margin-top: 5px;
  display: block;
}

.status-display {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 5px;
}

.status-display i {
  font-size: 14px;
}

.status-display strong {
  color: #495057;
}

/* Processing Status Styles */
.processing-status {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.processing-header {
  margin-bottom: 20px;
}

.processing-header h3 {
  color: #1a237e;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.processing-progress {
  margin-bottom: 20px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1a237e, #3f51b5);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.processing-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.processing-step {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #ddd;
  transition: all 0.3s ease;
}

.processing-step.active {
  background: #e3f2fd;
  border-left-color: #1a237e;
}

.processing-step.completed {
  background: #e8f5e8;
  border-left-color: #28a745;
}

.processing-step.error {
  background: #ffeaea;
  border-left-color: #dc3545;
}

.step-icon {
  font-size: 20px;
  color: #666;
  min-width: 24px;
}

.processing-step.active .step-icon {
  color: #1a237e;
}

.processing-step.completed .step-icon {
  color: #28a745;
}

.processing-step.error .step-icon {
  color: #dc3545;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
}

.step-status {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  font-weight: 500;
}

.processing-step.active .step-status {
  color: #1a237e;
}

.processing-step.completed .step-status {
  color: #28a745;
}

.processing-step.error .step-status {
  color: #dc3545;
}

/* Requirements Status */
.requirements-status {
  margin-top: 20px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 6px;
  text-align: center;
}

.requirements-status p {
  margin: 0;
  font-size: 14px;
  color: #6c757d;
}

/* Results Header */
.results-header {
  margin-bottom: 20px;
}

.results-header h3 {
  color: #28a745;
  display: flex;
  align-items: center;
  gap: 10px;
  margin: 0;
}

.audit-content {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding: 15px;
}

/* Report Downloads Section */
.report-downloads {
  margin-top: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.report-downloads h4 {
  color: #1a237e;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.report-downloads h4::before {
  content: '\f019';
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.download-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.download-buttons .btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 20px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.download-buttons .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.download-buttons .btn i {
  font-size: 16px;
}

/* Summary Cards Enhancement */
.summary-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-left: 4px solid #1a237e;
  transition: transform 0.3s ease;
}

.summary-card:hover {
  transform: translateY(-2px);
}

.card-icon {
  font-size: 24px;
  color: #1a237e;
  margin-bottom: 10px;
}

.card-content h4 {
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.card-value {
  font-size: 28px;
  font-weight: 700;
  color: #1a237e;
  margin: 0;
}

/* Report Manager Styles */
.report-manager-container {
  padding: 20px;
}

.report-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.header-content h2 {
  color: #1a237e;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.header-content p {
  color: #666;
  margin: 0;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 10px;
}

/* Reports Statistics */
.reports-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.reports-stats .stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  border-left: 4px solid #1a237e;
  display: flex;
  align-items: center;
  gap: 15px;
  transition: transform 0.3s ease;
}

.reports-stats .stat-card:hover {
  transform: translateY(-2px);
}

.stat-icon {
  font-size: 24px;
  color: #1a237e;
  min-width: 40px;
  text-align: center;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a237e;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Section Navigation */
.section-navigation {
  margin-bottom: 30px;
}

.section-navigation h3 {
  color: #1a237e;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
}

.section-tabs {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.section-tab {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.section-tab:hover {
  background: #e9ecef;
  border-color: #1a237e;
  color: #1a237e;
}

.section-tab.active {
  background: #1a237e;
  border-color: #1a237e;
  color: white;
}

.section-tab i {
  font-size: 12px;
}

.tab-count {
  background: #dc3545;
  color: white;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 10px;
  margin-left: 4px;
  min-width: 16px;
  text-align: center;
}

.section-tab.active .tab-count {
  background: rgba(255, 255, 255, 0.3);
  color: white;
}

/* PDF Sorter Report Info Fields */
.report-info-fields {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  margin-top: 10px;
}

.field-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.field-group label {
  font-size: 13px;
  font-weight: 600;
  color: #495057;
  margin-bottom: 5px;
}

.form-input {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  font-size: 14px;
  transition: border-color 0.3s ease;
  background: white;
}

.form-input:focus {
  outline: none;
  border-color: #1a237e;
  box-shadow: 0 0 0 2px rgba(26, 35, 126, 0.1);
}

.form-input::placeholder {
  color: #6c757d;
  font-style: italic;
}

/* Reports Filters */
.reports-filters {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.filter-group label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.filter-group .form-input {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

/* Reports Table */
.reports-table-container {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  margin-bottom: 30px;
}

.reports-table {
  width: 100%;
  border-collapse: collapse;
}

.reports-table thead {
  background: #1a237e;
  color: white;
}

.reports-table th {
  padding: 15px 12px;
  text-align: left;
  font-weight: 600;
  font-size: 13px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.reports-table td {
  padding: 12px;
  border-bottom: 1px solid #e9ecef;
  vertical-align: middle;
}

.reports-table tbody tr:hover {
  background: #f8f9fa;
}

.reports-table tbody tr:last-child td {
  border-bottom: none;
}

/* Report Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.completed {
  background: #d4edda;
  color: #155724;
}

.status-badge.processing {
  background: #fff3cd;
  color: #856404;
}

.status-badge.error {
  background: #f8d7da;
  color: #721c24;
}

/* Report Actions */
.report-actions {
  display: flex;
  gap: 5px;
}

.btn-icon {
  padding: 6px 8px;
  border: none;
  background: #f8f9fa;
  color: #666;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 12px;
}

.btn-icon:hover {
  background: #e9ecef;
  color: #333;
}

.btn-icon.download {
  color: #28a745;
}

.btn-icon.download:hover {
  background: #d4edda;
  color: #155724;
}

.btn-icon.view {
  color: #007bff;
}

.btn-icon.view:hover {
  background: #cce7ff;
  color: #0056b3;
}

.btn-icon.delete {
  color: #dc3545;
}

.btn-icon.delete:hover {
  background: #f8d7da;
  color: #721c24;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.empty-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 20px;
}

.empty-state h3 {
  color: #333;
  margin-bottom: 10px;
}

.empty-state p {
  color: #666;
  margin-bottom: 10px;
}

/* Loading State */
.loading-state {
  text-align: center;
  padding: 40px 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.loading-spinner {
  font-size: 24px;
  color: #1a237e;
  margin-bottom: 15px;
}

.loading-state p {
  color: #666;
  margin: 0;
}

/* Report Table Enhancements */
.report-date strong {
  display: block;
  color: #333;
  font-size: 14px;
}

.report-date small {
  color: #666;
  font-size: 12px;
}

.report-period strong {
  display: block;
  color: #1a237e;
  font-size: 14px;
}

.report-period small {
  color: #666;
  font-size: 12px;
}

.report-author strong {
  display: block;
  color: #333;
  font-size: 14px;
}

.report-author small {
  color: #666;
  font-size: 12px;
}

.employee-count {
  font-weight: 600;
  color: #1a237e;
}

.changes-count {
  font-weight: 600;
  color: #666;
}

.changes-count.has-changes {
  color: #dc3545;
}

.report-type-badge {
  display: inline-block;
  padding: 4px 8px;
  background: #e3f2fd;
  color: #1976d2;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

/* Tab Info Styling */
.tab-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.tab-badge {
  display: inline-block;
  padding: 4px 8px;
  background: #f0f8ff;
  color: #1a237e;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: capitalize;
  border: 1px solid #e3f2fd;
}

.tab-info small {
  color: #666;
  font-size: 10px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.tab-info small i {
  font-size: 9px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .reports-filters {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .reports-stats {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .report-manager-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .header-actions {
    justify-content: stretch;
  }

  .header-actions .btn {
    flex: 1;
  }

  .reports-stats {
    grid-template-columns: 1fr;
  }

  .reports-table-container {
    overflow-x: auto;
  }

  .reports-table {
    min-width: 800px;
  }
}

/* ===== PDF SORTER STYLES ===== */

/* PDF Sorter Container */
.pdf-sorter-container {
  max-width: 1200px;
  margin: 0 auto;
}

.pdf-sorter-header {
  text-align: center;
  margin-bottom: 30px;
}

.pdf-sorter-header h2 {
  color: #1a237e;
  font-size: 28px;
  margin-bottom: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.tab-description {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* Upload Section */
.pdf-sorter-upload-section {
  margin-bottom: 30px;
}

.upload-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
}

.upload-header {
  background: linear-gradient(135deg, #f8f9ff 0%, #e8f0ff 100%);
  padding: 20px;
  border-bottom: 1px solid #e0e8ff;
}

.upload-header h3 {
  color: #1a237e;
  font-size: 20px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.upload-header p {
  color: #666;
  margin: 0;
}

#pdf-upload-area {
  margin: 20px;
  border: 2px solid #d0d7de;
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  background: #fafbfc;
  transition: all 0.3s ease;
}

#pdf-upload-area:hover {
  border-color: #1a237e;
  background: #f8f9ff;
  transform: translateY(-2px);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
}

.upload-icon {
  font-size: 48px;
  color: #1a237e;
  opacity: 0.7;
}

.upload-text {
  color: #666;
  font-size: 16px;
  margin: 0;
}

/* Enhanced Button Styles for PDF Sorter */
.pdf-sorter-container .btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
  text-decoration: none;
}

.pdf-sorter-container .btn-primary {
  background: #1a237e;
  color: white;
}

.pdf-sorter-container .btn-primary:hover {
  background: #303f9f;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(26, 35, 126, 0.3);
}

.pdf-sorter-container .btn-secondary {
  background: #6c757d;
  color: white;
}

.pdf-sorter-container .btn-secondary:hover {
  background: #5a6268;
}

.pdf-sorter-container .btn-success {
  background: #28a745;
  color: white;
}

.pdf-sorter-container .btn-success:hover {
  background: #218838;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.pdf-sorter-container .btn-danger {
  background: #dc3545;
  color: white;
}

.pdf-sorter-container .btn-danger:hover {
  background: #c82333;
}

.pdf-sorter-container .btn-outline-primary {
  background: transparent;
  color: #1a237e;
  border: 2px solid #1a237e;
}

.pdf-sorter-container .btn-outline-primary:hover {
  background: #1a237e;
  color: white;
}

.pdf-sorter-container .btn-lg {
  padding: 16px 32px;
  font-size: 16px;
}

.pdf-sorter-container .btn-sm {
  padding: 8px 16px;
  font-size: 12px;
}

/* File Info Display */
.pdf-sorter-container .file-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 20px;
  padding: 15px;
  background: #e8f5e8;
  border-radius: 8px;
  border-left: 4px solid #28a745;
}

.file-details {
  flex: 1;
}

.file-name {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #2e7d32;
  margin-bottom: 5px;
}

.file-stats {
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #4caf50;
}

/* Configuration Section */
.pdf-sorter-config-section {
  margin-bottom: 30px;
}

.config-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
}

.config-header {
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  padding: 20px;
  border-bottom: 1px solid #ffcc02;
}

.config-header h3 {
  color: #e65100;
  font-size: 20px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.config-header p {
  color: #bf360c;
  margin: 0;
}

/* Sorting Levels */
.sorting-level {
  padding: 25px;
  border-bottom: 1px solid #f0f0f0;
}

.sorting-level:last-child {
  border-bottom: none;
}

.level-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
}

.level-header h4 {
  color: #333;
  font-size: 18px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
}

.level-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.level-badge:not(.optional) {
  background: #e3f2fd;
  color: #1976d2;
}

.level-badge.optional {
  background: #f3e5f5;
  color: #7b1fa2;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: #1a237e;
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* Change Detection Toggle Styles */
.change-detection-header {
  font-size: 11px;
  text-align: center;
  padding: 8px 4px;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1565c0;
  font-weight: 600;
  border: 1px solid #90caf9;
  white-space: nowrap;
  min-width: 70px;
}

.change-detection-cell {
  text-align: center;
  padding: 8px 4px;
  vertical-align: middle;
  background: #fafafa;
  border: 1px solid #e0e0e0;
}

.mini-toggle {
  position: relative;
  display: inline-block;
  width: 32px;
  height: 16px;
}

.mini-toggle input {
  opacity: 0;
  width: 0;
  height: 0;
}

.mini-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.2s;
  border-radius: 16px;
}

.mini-slider:before {
  position: absolute;
  content: "";
  height: 12px;
  width: 12px;
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: 0.2s;
  border-radius: 50%;
}

input:checked + .mini-slider {
  background-color: #4caf50;
}

input:checked + .mini-slider:before {
  transform: translateX(16px);
}

/* Special styling for NO_CHANGE column */
.change-detection-header:nth-child(6) {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc80 100%);
  color: #ef6c00;
  border-color: #ffb74d;
}

.change-detection-cell:nth-child(6) .mini-slider {
  background-color: #ff9800;
}

.change-detection-cell:nth-child(6) input:checked + .mini-slider {
  background-color: #4caf50;
}

/* Responsive adjustments for change detection columns */
@media (max-width: 1200px) {
  .change-detection-header {
    font-size: 10px;
    padding: 6px 2px;
    min-width: 60px;
  }

  .mini-toggle {
    width: 28px;
    height: 14px;
  }

  .mini-slider:before {
    height: 10px;
    width: 10px;
  }

  input:checked + .mini-slider:before {
    transform: translateX(14px);
  }
}

/* Sorting Options */
.sorting-options {
  display: grid;
  gap: 15px;
}

.sort-option {
  position: relative;
}

.sort-option input[type="radio"] {
  position: absolute;
  opacity: 0;
  cursor: pointer;
}

.sort-option label {
  display: flex;
  align-items: flex-start;
  gap: 15px;
  padding: 20px;
  border: 2px solid #e0e0e0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.sort-option label:hover {
  border-color: #1a237e;
  background: #f8f9ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(26, 35, 126, 0.1);
}

.sort-option input[type="radio"]:checked + label {
  border-color: #1a237e;
  background: #f0f4ff;
  box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
}

.sort-option label i {
  font-size: 24px;
  color: #1a237e;
  margin-top: 2px;
}

.option-content {
  flex: 1;
}

.option-title {
  font-weight: 600;
  color: #333;
  font-size: 16px;
  display: block;
  margin-bottom: 5px;
}

.option-desc {
  color: #666;
  font-size: 14px;
  line-height: 1.4;
}

/* Form Select */
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.form-select:focus {
  outline: none;
  border-color: #1a237e;
  box-shadow: 0 0 0 3px rgba(26, 35, 126, 0.1);
}

/* Sort Order Options */
.sort-order-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 15px;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: #fafafa;
}

.radio-option:hover {
  border-color: #1a237e;
  background: #f8f9ff;
}

.radio-option input[type="radio"] {
  display: none;
}

.radio-custom {
  width: 20px;
  height: 20px;
  border: 2px solid #ccc;
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.radio-option input[type="radio"]:checked + .radio-custom {
  border-color: #1a237e;
  background: #1a237e;
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
}

.radio-option i {
  color: #1a237e;
  font-size: 18px;
}

/* Config Actions */
.config-actions {
  padding: 25px;
  background: #f8f9fa;
  display: flex;
  gap: 15px;
  justify-content: center;
}

/* Processing Section */
.pdf-sorter-processing-section {
  margin-bottom: 30px;
}

.processing-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
}

.processing-header {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #2196f3;
}

.processing-header h3 {
  color: #1976d2;
  font-size: 20px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.processing-header p {
  color: #1565c0;
  margin: 0;
}

.progress-container {
  padding: 30px;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #e0e0e0;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 15px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #1a237e 0%, #3f51b5 100%);
  border-radius: 6px;
  transition: width 0.3s ease;
  width: 0%;
}

.progress-text {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

#sort-progress-percentage {
  font-weight: 600;
  color: #1a237e;
}

#sort-progress-status {
  color: #666;
}

.processing-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  padding: 0 30px 20px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #1a237e;
}

.stat-label {
  color: #666;
  font-weight: 500;
}

.stat-value {
  color: #1a237e;
  font-weight: 600;
  font-size: 16px;
}

.processing-actions {
  padding: 20px 30px;
  text-align: center;
  background: #f8f9fa;
}

/* Top-level Phase Progress Bar */
.phase-progress-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #fff;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.05);
  margin-bottom: 15px;
  position: relative;
}

.phase-progress-bar .phase-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 16.66%;
  position: relative;
}

.phase-progress-bar .phase-dot {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: #f5f5f5;
  border: 2px solid #e0e0e0;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  position: relative;
  z-index: 2;
  color: #757575;
}

.phase-progress-bar .phase-indicator.active .phase-dot {
  background: #bbdefb;
  border-color: #1976d2;
  color: #1976d2;
}

.phase-progress-bar .phase-indicator.completed .phase-dot {
  background: #a5d6a7;
  border-color: #4caf50;
  color: #4caf50;
}

.phase-progress-bar .phase-name {
  font-size: 11px;
  font-weight: 600;
  text-align: center;
  color: #757575;
  text-transform: uppercase;
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.phase-progress-bar .phase-status {
  font-size: 9px;
  color: #9e9e9e;
  text-align: center;
}

.phase-progress-bar .phase-indicator.active .phase-name {
  color: #1976d2;
}

.phase-progress-bar .phase-indicator.active .phase-status {
  color: #1976d2;
  font-weight: 600;
}

.phase-progress-bar .phase-indicator.completed .phase-name {
  color: #4caf50;
}

.phase-progress-bar .phase-indicator.completed .phase-status {
  color: #4caf50;
}

/* Results Section */
.pdf-sorter-results-section {
  margin-bottom: 30px;
}

.results-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0,0,0,0.1);
  overflow: hidden;
}

.results-header {
  background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
  padding: 20px;
  text-align: center;
  border-bottom: 1px solid #4caf50;
}

.results-header h3 {
  color: #2e7d32;
  font-size: 20px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.results-header p {
  color: #388e3c;
  margin: 0;
}

.results-summary {
  padding: 30px;
}

.summary-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 15px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #28a745;
}

.summary-icon {
  width: 50px;
  height: 50px;
  background: #28a745;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.summary-content {
  flex: 1;
}

.summary-label {
  display: block;
  color: #666;
  font-size: 14px;
  margin-bottom: 5px;
}

.summary-value {
  display: block;
  color: #333;
  font-size: 18px;
  font-weight: 600;
}

.sorting-preview {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.sorting-preview h4 {
  color: #333;
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  gap: 10px;
}

.preview-list {
  max-height: 300px;
  overflow-y: auto;
}

.preview-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: white;
  border-radius: 6px;
  margin-bottom: 8px;
  border-left: 3px solid #28a745;
}

.preview-employee {
  font-weight: 500;
  color: #333;
}

.preview-details {
  color: #666;
  font-size: 14px;
}

.results-actions {
  padding: 25px 30px;
  background: #f8f9fa;
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive Design for PDF Sorter */
@media (max-width: 768px) {
  .pdf-sorter-container {
    padding: 0 10px;
  }

  .sort-order-options {
    grid-template-columns: 1fr;
  }

  .summary-stats {
    grid-template-columns: 1fr;
  }

  .config-actions,
  .results-actions {
    flex-direction: column;
    align-items: center;
  }

  .pdf-sorter-container .btn {
    width: 100%;
    max-width: 300px;
  }

  .processing-stats {
    grid-template-columns: 1fr;
  }

  .sort-option label {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }

  .sort-option label i {
    font-size: 32px;
  }
}

/* Data Builder Styles */
.data-builder-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.data-builder-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #1a237e, #3f51b5);
  color: white;
  border-radius: 12px;
}

.data-builder-header h2 {
  margin: 0 0 10px 0;
  font-size: 2.2em;
}

.data-builder-header .description {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
}

.data-builder-section {
  background: white;
  border-radius: 12px;
  padding: 25px;
  margin-bottom: 25px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

/* Special styling for file upload section */
.data-builder-section:has(h3:contains("Upload Payroll File")),
.data-builder-section:first-of-type {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
}

.data-builder-section:has(h3:contains("Upload Payroll File")) h3,
.data-builder-section:first-of-type h3 {
  color: white;
  border-bottom-color: rgba(255, 255, 255, 0.3);
}

.data-builder-section h3 {
  color: #1a237e;
  margin: 0 0 20px 0;
  font-size: 1.4em;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 10px;
}

/* Enhanced upload area for Data Builder */
.data-builder-section .upload-area {
  border: 2px dashed rgba(255, 255, 255, 0.4);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.data-builder-section .upload-area:hover {
  border-color: rgba(255, 255, 255, 0.7);
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
}

.data-builder-section .upload-area .upload-icon {
  color: rgba(255, 255, 255, 0.8);
  font-size: 48px;
  margin-bottom: 15px;
}

.data-builder-section .upload-area .upload-text {
  color: rgba(255, 255, 255, 0.9);
  font-size: 16px;
  margin-bottom: 20px;
}

.data-builder-section .upload-area .btn {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.4);
  color: white;
  font-weight: 600;
}

.data-builder-section .upload-area .btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.6);
  transform: translateY(-1px);
}

/* Dictionary Table Styles */
.dictionary-table-container {
  margin-top: 20px;
}

.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
}

.control-group {
  display: flex;
  align-items: center;
  gap: 10px;
}

.control-group label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  cursor: pointer;
}

.table-wrapper {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.dictionary-table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.dictionary-table th {
  background: #1a237e;
  color: white;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  position: sticky;
  top: 0;
  z-index: 10;
}

.dictionary-table td {
  padding: 10px 12px;
  border-bottom: 1px solid #eee;
  vertical-align: middle;
}

.dictionary-table tr:hover {
  background: #f8f9ff;
}

.dictionary-table input[type="checkbox"] {
  transform: scale(1.2);
  cursor: pointer;
}

/* Build Controls */
.build-controls {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.build-controls .control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.build-controls label {
  font-weight: 500;
  color: #333;
}

.build-controls input[type="month"] {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 14px;
}

.build-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn-large {
  padding: 12px 30px;
  font-size: 16px;
  font-weight: 600;
}

/* Enhanced Real-Time Progress Panel (A2) */
.enhanced-progress-panel {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9ff 100%);
  border-radius: 16px;
  border: 2px solid #1976d2;
  box-shadow: 0 8px 32px rgba(25, 118, 210, 0.15);
  margin: 10px 0;
  padding: 0;
  overflow: hidden;
  animation: slideInUp 0.5s ease-out;
}

/* Phase Progress Indicators at Top */
.phase-progress-indicators-top {
  background: linear-gradient(135deg, #f8f9ff 0%, #e3f2fd 100%);
  padding: 15px 20px;
  border-bottom: 1px solid #e1f5fe;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 4px;
  flex-wrap: wrap;
}

.phase-progress-indicators-top .phase-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  flex: 1;
  min-width: 80px;
  position: relative;
}

.phase-progress-indicators-top .phase-indicator:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 15px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.phase-progress-indicators-top .phase-indicator.active:not(:last-child)::after {
  background: linear-gradient(90deg, #1976d2, #e0e0e0);
}

.phase-progress-indicators-top .phase-indicator.completed:not(:last-child)::after {
  background: #4caf50;
}

.phase-progress-indicators-top .phase-dot {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #e0e0e0;
  border: 3px solid #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
  color: white;
  position: relative;
  z-index: 2;
  transition: all 0.3s ease;
}

.phase-progress-indicators-top .phase-dot i {
  font-size: 12px;
  color: inherit;
}

.phase-progress-indicators-top .phase-indicator.pending .phase-dot {
  background: #e0e0e0;
  color: #757575;
}

.phase-progress-indicators-top .phase-indicator.pending .phase-dot i {
  display: block;
  font-size: 12px;
  color: #757575;
}

.phase-progress-indicators-top .phase-indicator.active .phase-dot {
  background: #1976d2;
  color: white;
  animation: pulse 2s infinite;
  box-shadow: 0 0 0 0 rgba(25, 118, 210, 0.7);
}

.phase-progress-indicators-top .phase-indicator.active .phase-dot i {
  display: block;
  font-size: 12px;
  color: white;
}

.phase-progress-indicators-top .phase-indicator.completed .phase-dot {
  background: #4caf50;
  color: white;
}

.phase-progress-indicators-top .phase-indicator.completed .phase-dot::before {
  content: '✓';
  font-size: 14px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10;
}

.phase-progress-indicators-top .phase-indicator.completed .phase-dot i {
  display: none;
}

.phase-progress-indicators-top .phase-name {
  font-size: 12px;
  font-weight: 600;
  color: #424242;
  text-align: center;
  line-height: 1.2;
}

.phase-progress-indicators-top .phase-indicator.active .phase-name {
  color: #1976d2;
}

.phase-progress-indicators-top .phase-indicator.completed .phase-name {
  color: #4caf50;
}

.phase-progress-indicators-top .phase-status {
  font-size: 10px;
  color: #757575;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.phase-progress-indicators-top .phase-indicator.active .phase-status {
  color: #1976d2;
  font-weight: 600;
}

.phase-progress-indicators-top .phase-indicator.completed .phase-status {
  color: #4caf50;
  font-weight: 600;
}

.progress-header {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 20px 30px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.current-phase-indicator {
  display: flex;
  align-items: center;
  gap: 15px;
}

.phase-icon {
  width: 50px;
  height: 50px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  animation: pulse 2s infinite;
}

.phase-info h3 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
}

.phase-info p {
  margin: 5px 0 0 0;
  opacity: 0.9;
  font-size: 14px;
}

.progress-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.control-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 6px;
}

.pause-btn {
  background: #ff9800;
  color: white;
}

.pause-btn:hover {
  background: #f57c00;
  transform: translateY(-1px);
}

.stop-btn {
  background: #f44336;
  color: white;
}

.stop-btn:hover {
  background: #d32f2f;
  transform: translateY(-1px);
}

.processing-timer {
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 12px;
  border-radius: 6px;
  font-weight: 600;
  font-size: 14px;
}

.realtime-progress-container {
  padding: 15px 30px;
  background: white;
}

.progress-bar-enhanced {
  position: relative;
  width: 100%;
  height: 30px;
  background: #e3f2fd;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-fill-enhanced {
  height: 100%;
  background: linear-gradient(90deg, #1976d2, #42a5f5, #1976d2);
  background-size: 200% 100%;
  width: 0%;
  transition: width 0.5s ease;
  border-radius: 15px;
  animation: shimmer 2s infinite;
}

.progress-text-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  color: #1976d2;
  font-size: 16px;
  text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
}

.progress-status-text {
  text-align: center;
  margin-top: 15px;
  font-size: 14px;
  color: #666;
  font-style: italic;
}

.live-stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 15px;
  padding: 15px 30px;
  background: #f8f9ff;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  gap: 15px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 45px;
  height: 45px;
  background: linear-gradient(135deg, #1976d2, #42a5f5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #1976d2;
  margin: 0;
  line-height: 1;
  transition: transform 0.2s ease, color 0.3s ease;
}

.stat-value:hover {
  color: #1565c0;
}

.stat-label {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.activity-feed {
  padding: 15px 30px;
  background: white;
  border-top: 1px solid #e0e0e0;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.activity-header h4 {
  margin: 0;
  color: #1976d2;
  font-size: 16px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.clear-btn {
  background: #f5f5f5;
  border: none;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 12px;
  color: #666;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-btn:hover {
  background: #e0e0e0;
  color: #333;
}

.activity-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.activity-item {
  padding: 12px 15px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  align-items: center;
  gap: 10px;
  animation: fadeInLeft 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: white;
  flex-shrink: 0;
}

.activity-icon.info {
  background: #2196f3;
}

.activity-icon.success {
  background: #4caf50;
}

.activity-icon.warning {
  background: #ff9800;
}

.activity-icon.error {
  background: #f44336;
}

.activity-content {
  flex: 1;
}

.activity-message {
  font-size: 13px;
  color: #333;
  margin: 0;
}

.activity-time {
  font-size: 11px;
  color: #999;
  margin-top: 2px;
}

.phase-progress-indicators {
  padding: 20px 30px;
  background: #f8f9ff;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
}

.phase-indicator {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  flex: 1;
  position: relative;
}

.phase-indicator:not(:last-child)::after {
  content: '';
  position: absolute;
  top: 12px;
  right: -50%;
  width: 100%;
  height: 2px;
  background: #e0e0e0;
  z-index: 1;
}

.phase-indicator.active::after {
  background: #1976d2;
}

.phase-indicator.completed::after {
  background: #4caf50;
}

.phase-dot {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e0e0e0;
  border: 3px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  z-index: 2;
  position: relative;
}

.phase-indicator.active .phase-dot {
  background: #1976d2;
  animation: pulse 2s infinite;
}

.phase-indicator.completed .phase-dot {
  background: #4caf50;
}

.phase-name {
  font-size: 11px;
  font-weight: 600;
  color: #666;
  text-align: center;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.phase-indicator.active .phase-name {
  color: #1976d2;
}

.phase-indicator.completed .phase-name {
  color: #4caf50;
}

.phase-status {
  font-size: 10px;
  color: #999;
  text-align: center;
}

.phase-indicator.active .phase-status {
  color: #1976d2;
  font-weight: 600;
}

.phase-indicator.completed .phase-status {
  color: #4caf50;
  font-weight: 600;
}

/* Animations */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Results Section */
.results-section {
  background: #f0fff4;
  border: 2px solid #28a745;
  border-radius: 12px;
  padding: 25px;
}

.results-section h3 {
  color: #28a745;
  margin-bottom: 20px;
}

.results-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 25px;
}

.summary-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ddd;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden; /* Prevent content overflow */
  word-wrap: break-word; /* Break long words */
}

.summary-card h4 {
  color: #1a237e;
  margin: 0 0 15px 0;
  font-size: 1.2em;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.summary-card p {
  margin: 8px 0;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Data Builder specific file info styling */
.data-builder-section .file-info {
  background: #e8f5e8;
  border: 1px solid #4caf50;
  border-radius: 8px;
  padding: 15px;
  margin-top: 15px;
  overflow: hidden;
}

.data-builder-section .file-info .file-details h4 {
  color: #2e7d32;
  margin: 0 0 10px 0;
  font-size: 1.1em;
  display: flex;
  align-items: center;
  gap: 8px;
}

.data-builder-section .file-info .file-details p {
  margin: 5px 0;
  color: #388e3c;
  font-size: 14px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.results-actions {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* Analytics Section */
.analytics-section {
  background: #fff8e1;
  border: 2px solid #ff9800;
  border-radius: 12px;
  padding: 25px;
}

.analytics-section h3 {
  color: #ff9800;
  margin-bottom: 20px;
}

.analytics-tabs {
  display: flex;
  gap: 5px;
  margin-bottom: 20px;
  border-bottom: 2px solid #e0e0e0;
}

.analytics-tab {
  padding: 12px 20px;
  background: #f5f5f5;
  border: none;
  border-radius: 8px 8px 0 0;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s ease;
}

.analytics-tab:hover {
  background: #e0e0e0;
}

.analytics-tab.active {
  background: #ff9800;
  color: white;
}

.analytics-content {
  min-height: 300px;
}

.analytics-panel {
  display: none;
  padding: 20px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.analytics-panel.active {
  display: block;
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.status-fixed {
  background: #e8f5e8;
  color: #2e7d32;
}

.status-varying {
  background: #fff3e0;
  color: #f57c00;
}

.status-auto {
  background: #e3f2fd;
  color: #1976d2;
}

/* Analytics Grid and Cards */
.analytics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.analytics-card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ddd;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analytics-card h4 {
  color: #666;
  margin: 0 0 10px 0;
  font-size: 14px;
  font-weight: 500;
}

.analytics-value {
  color: #1a237e;
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.analytics-insights {
  background: white;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #ddd;
}

.analytics-insights h4 {
  color: #1a237e;
  margin: 0 0 15px 0;
}

.analytics-insights ul {
  margin: 0;
  padding-left: 20px;
}

.analytics-insights li {
  margin-bottom: 8px;
  color: #333;
}

/* Responsive Design for Data Builder */
@media (max-width: 768px) {
  .data-builder-container {
    padding: 15px;
  }

  .build-controls {
    grid-template-columns: 1fr;
  }

  .results-summary {
    grid-template-columns: 1fr;
  }

  .analytics-tabs {
    flex-wrap: wrap;
  }

  .analytics-grid {
    grid-template-columns: 1fr;
  }

  .build-actions,
  .results-actions {
    flex-direction: column;
    align-items: center;
  }
}

/* ========== BANK ADVISER SUB-TABS ========== */
.bank-adviser-main-container {
  max-width: 1400px;
  margin: 0 auto;
}

.bank-adviser-header {
  text-align: center;
  margin-bottom: 30px;
}

.bank-adviser-header h2 {
  color: #2c3e50;
  font-size: 2em;
  margin-bottom: 10px;
}

.bank-adviser-header .tab-description {
  color: #6c757d;
  font-size: 1.1em;
  margin-bottom: 0;
}

.bank-adviser-tabs {
  display: flex;
  justify-content: center;
  gap: 10px;
  margin-bottom: 30px;
  border-bottom: 2px solid #e9ecef;
  padding-bottom: 0;
}

.bank-adviser-tab-btn {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-bottom: none;
  padding: 15px 25px;
  cursor: pointer;
  border-radius: 8px 8px 0 0;
  font-weight: 500;
  color: #495057;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
}

.bank-adviser-tab-btn:hover {
  background: #e9ecef;
  color: #2c3e50;
}

.bank-adviser-tab-btn.active {
  background: #3498db;
  color: white;
  border-color: #3498db;
  transform: translateY(2px);
}

.bank-adviser-tab-btn i {
  font-size: 1.2em;
}

.bank-adviser-subtab-content {
  display: none;
  animation: fadeIn 0.3s ease-in;
}

.bank-adviser-subtab-content.active {
  display: block;
}

/* Responsive Design for Bank Adviser */
@media (max-width: 768px) {
  .bank-adviser-tabs {
    flex-direction: column;
    align-items: center;
  }

  .bank-adviser-tab-btn {
    border-radius: 6px;
    border: 1px solid #dee2e6;
    margin-bottom: 5px;
  }
}

/* Transform Screen Styles - Embedded Version */
.transform-screen {
  position: relative;
  width: 100%;
  margin: 20px 0;
  background: transparent;
  z-index: 1;
  display: block;
}

.transform-container {
  background: white;
  border-radius: 12px;
  width: 100%;
  max-width: none;
  max-height: none;
  overflow-y: visible;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
}

.transform-header {
  background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px 12px 0 0;
}

.transform-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.transform-subtitle {
  font-size: 14px;
  opacity: 0.9;
  margin-top: 5px;
}

.transform-actions {
  display: flex;
  gap: 10px;
}

.transform-progress {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.transform-progress .progress-bar {
  background: #f0f0f0;
  border-radius: 10px;
  height: 8px;
  overflow: hidden;
  margin-bottom: 10px;
}

.transform-progress .progress-fill {
  background: linear-gradient(90deg, #1a237e, #3f51b5);
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 10px;
}

.transform-progress .progress-text {
  font-size: 14px;
  color: #666;
  text-align: center;
}

.transform-details {
  padding: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.details-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 15px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 6px;
}

.detail-label {
  font-weight: 500;
  color: #666;
}

.detail-value {
  font-weight: 600;
  color: #1a237e;
}

.transform-log {
  padding: 20px;
  max-height: 300px;
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 10px;
  background: #f8f9fa;
}

.log-entry {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #e9ecef;
  font-size: 13px;
}

.log-entry:last-child {
  border-bottom: none;
}

.log-time {
  color: #666;
  font-size: 11px;
  min-width: 60px;
}

.log-icon {
  min-width: 20px;
}

.log-message {
  flex: 1;
}

.log-entry.log-success {
  color: #28a745;
}

.log-entry.log-error {
  color: #dc3545;
}

.log-entry.log-warning {
  color: #ffc107;
}

.log-entry.log-info {
  color: #17a2b8;
}

/* Generated Reports Section */
.generated-reports {
  margin-top: 20px;
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.generated-reports h4 {
  margin-bottom: 15px;
  color: #333;
  display: flex;
  align-items: center;
  gap: 10px;
}

.reports-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.report-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.report-item:hover {
  background: #e9ecef;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.report-info {
  flex: 1;
}

.report-name {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: 600;
  color: #333;
  margin-bottom: 5px;
}

.report-name i {
  color: #28a745;
}

.report-details {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #666;
}

.report-type {
  background: #1a237e;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
}

.report-actions {
  display: flex;
  gap: 8px;
}

.eye-btn,
.folder-btn {
  background: #1a237e;
  color: white;
  border: none;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.eye-btn:hover {
  background: #303f9f;
  transform: scale(1.05);
}

.folder-btn {
  background: #6c757d;
}

.folder-btn:hover {
  background: #5a6268;
  transform: scale(1.05);
}

/* Period Section Styles */
.period-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.period-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.period-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 15px;
  background: #f8f9fa;
  border-radius: 6px;
  font-size: 14px;
  color: #666;
}

.period-status i {
  font-size: 16px;
}

/* Success and Error Message Styles for Word Document Generation */
.success-message, .error-message {
  margin-bottom: 15px;
  z-index: 1000;
}

.alert {
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid transparent;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  line-height: 1.4;
}

.alert-success {
  color: #155724;
  background-color: #d4edda;
  border-color: #c3e6cb;
}

.alert-danger {
  color: #721c24;
  background-color: #f8d7da;
  border-color: #f5c6cb;
}

.alert .close {
  background: none;
  border: none;
  font-size: 18px;
  font-weight: bold;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  padding: 0;
  margin-left: 10px;
}

.alert .close:hover {
  opacity: 1;
}

/* Word Document Generation Button Styles */
.preview-actions {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
  flex-wrap: wrap;
}

.preview-actions .btn {
  min-width: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.preview-actions .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.preview-actions .btn i.fa-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

