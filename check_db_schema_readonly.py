#!/usr/bin/env python3
"""
READ-ONLY database schema check - NO MODIFICATIONS
"""

import sqlite3

def check_schema_readonly():
    """Check existing database schema WITHOUT making any changes"""
    print("🔍 READ-ONLY DATABASE SCHEMA CHECK")
    print("=" * 50)
    print("⚠️  NO MODIFICATIONS WILL BE MADE")
    print("=" * 50)
    
    try:
        conn = sqlite3.connect('./data/templar_payroll_auditor.db')
        cursor = conn.cursor()
        
        # Check current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()
        
        if current_session:
            session_id = current_session[0]
            print(f"✅ Current Session: {session_id}")
            
            # Check comparison_results table structure
            cursor.execute('PRAGMA table_info(comparison_results)')
            columns = cursor.fetchall()
            
            print("\n📋 Comparison Results Table Columns:")
            for col in columns:
                print(f"  - {col[1]} ({col[2]})")
            
            # Count total records
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            total_count = cursor.fetchone()[0]
            print(f"\n📊 Total Records: {total_count}")
            
            # Get sample record to understand data structure
            cursor.execute('SELECT * FROM comparison_results WHERE session_id = ? LIMIT 1', (session_id,))
            sample = cursor.fetchone()
            
            if sample:
                print("\n🔍 Sample Record Structure:")
                for i, col in enumerate(columns):
                    value = sample[i] if i < len(sample) else 'NULL'
                    print(f"  {col[1]}: {value}")
            
            print(f"\n✅ Schema check complete - NO CHANGES MADE")
            return True
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    check_schema_readonly()
