#!/usr/bin/env python3
"""
Investigate Auto-Learning Database State
Check if approved items are being saved correctly and identify the breakdown in the workflow
"""

import sys
import os
import sqlite3
import json
from datetime import datetime

def get_database_path():
    """Get the database path"""
    db_paths = [
        r"C:\THE PAYROLL AUDITOR\data\templar_payroll_auditor.db",
        r"data\templar_payroll_auditor.db",
        r"templar_payroll_auditor.db"
    ]
    
    for path in db_paths:
        if os.path.exists(path):
            return path
    return None

def investigate_auto_learning_database():
    """Investigate the auto-learning database state"""
    print("🔍 INVESTIGATING AUTO-LEARNING DATABASE STATE")
    print("=" * 70)
    
    try:
        db_path = get_database_path()
        if not db_path:
            print("❌ Database not found")
            return False
        
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"✅ Connected to database: {db_path}")
        
        # 1. Check if auto_learning_results table exists
        print("\n📊 1. CHECKING AUTO-LEARNING TABLES:")
        print("-" * 40)
        
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%auto%'")
        auto_tables = cursor.fetchall()
        
        if auto_tables:
            print(f"✅ Found {len(auto_tables)} auto-learning related tables:")
            for table in auto_tables:
                print(f"   - {table[0]}")
        else:
            print("❌ No auto-learning tables found")
            return False
        
        # 2. Check auto_learning_results table structure
        print("\n📊 2. AUTO_LEARNING_RESULTS TABLE STRUCTURE:")
        print("-" * 40)
        
        cursor.execute("PRAGMA table_info(auto_learning_results)")
        columns = cursor.fetchall()
        
        if columns:
            print("✅ Table structure:")
            for col in columns:
                print(f"   {col[1]} ({col[2]})")
        else:
            print("❌ auto_learning_results table not found")
            return False
        
        # 3. Check current data in auto_learning_results
        print("\n📊 3. AUTO_LEARNING_RESULTS DATA:")
        print("-" * 40)
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results")
        total_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE auto_approved = 1")
        approved_count = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM auto_learning_results WHERE dictionary_updated = 1")
        dict_updated_count = cursor.fetchone()[0]
        
        print(f"   Total items: {total_count}")
        print(f"   Approved items: {approved_count}")
        print(f"   Dictionary updated items: {dict_updated_count}")
        
        # 4. Check recent approved items
        print("\n📊 4. RECENT APPROVED ITEMS:")
        print("-" * 40)
        
        cursor.execute('''
            SELECT id, section_name, item_label, confidence_score, auto_approved, 
                   dictionary_updated, created_at
            FROM auto_learning_results 
            WHERE auto_approved = 1 
            ORDER BY created_at DESC 
            LIMIT 10
        ''')
        
        recent_approved = cursor.fetchall()
        
        if recent_approved:
            print(f"✅ Found {len(recent_approved)} recent approved items:")
            for item in recent_approved:
                id_val, section, label, confidence, approved, dict_updated, created = item
                print(f"   ID:{id_val} | {section}.{label} | Confidence:{confidence} | Dict Updated:{dict_updated} | Created:{created}")
        else:
            print("❌ No approved items found")
        
        # 5. Check if approved items are in dictionary_items table
        print("\n📊 5. CHECKING DICTIONARY_ITEMS INTEGRATION:")
        print("-" * 40)
        
        cursor.execute('''
            SELECT COUNT(*) FROM dictionary_items 
            WHERE auto_learned = 1
        ''')
        
        auto_learned_in_dict = cursor.fetchone()[0]
        print(f"   Auto-learned items in dictionary_items: {auto_learned_in_dict}")
        
        # Check for specific approved items in dictionary
        if recent_approved:
            print("\n   Checking if recent approved items are in dictionary_items:")
            for item in recent_approved[:5]:  # Check first 5
                id_val, section, label, confidence, approved, dict_updated, created = item
                
                cursor.execute('''
                    SELECT di.id, ds.section_name, di.item_name, di.auto_learned
                    FROM dictionary_items di
                    JOIN dictionary_sections ds ON di.section_id = ds.id
                    WHERE ds.section_name = ? AND di.item_name = ?
                ''', (section, label))
                
                dict_item = cursor.fetchone()
                
                if dict_item:
                    print(f"     ✅ {section}.{label} -> Found in dictionary (ID:{dict_item[0]}, Auto-learned:{dict_item[3]})")
                else:
                    print(f"     ❌ {section}.{label} -> NOT found in dictionary")
        
        # 6. Check for transfer issues
        print("\n📊 6. IDENTIFYING TRANSFER ISSUES:")
        print("-" * 40)
        
        # Find approved items that should be in dictionary but aren't
        cursor.execute('''
            SELECT a.id, a.section_name, a.item_label, a.confidence_score
            FROM auto_learning_results a
            LEFT JOIN dictionary_items d ON (
                d.item_name = a.item_label AND 
                d.section_id = (SELECT id FROM dictionary_sections WHERE section_name = a.section_name)
            )
            WHERE a.auto_approved = 1 AND a.dictionary_updated = 1 AND d.id IS NULL
        ''')
        
        missing_items = cursor.fetchall()
        
        if missing_items:
            print(f"⚠️ Found {len(missing_items)} approved items missing from dictionary:")
            for item in missing_items[:10]:  # Show first 10
                print(f"     {item[1]}.{item[2]} (ID:{item[0]}, Confidence:{item[3]})")
        else:
            print("✅ All approved items are properly transferred to dictionary")
        
        # 7. Check dictionary_sections table
        print("\n📊 7. DICTIONARY_SECTIONS TABLE:")
        print("-" * 40)
        
        cursor.execute("SELECT id, section_name FROM dictionary_sections ORDER BY section_name")
        sections = cursor.fetchall()
        
        if sections:
            print(f"✅ Found {len(sections)} dictionary sections:")
            for section in sections:
                print(f"   {section[0]}: {section[1]}")
        else:
            print("❌ No dictionary sections found")
        
        # 8. Summary and recommendations
        print("\n💡 ANALYSIS SUMMARY:")
        print("-" * 40)
        
        if total_count == 0:
            print("❌ ISSUE: No auto-learning data found")
            print("   Recommendation: Check if auto-learning extraction is working")
        elif approved_count == 0:
            print("❌ ISSUE: No approved items found")
            print("   Recommendation: Check approval workflow")
        elif auto_learned_in_dict == 0:
            print("❌ ISSUE: No auto-learned items in dictionary")
            print("   Recommendation: Check dictionary transfer mechanism")
        elif len(missing_items) > 0:
            print(f"⚠️ ISSUE: {len(missing_items)} approved items not transferred to dictionary")
            print("   Recommendation: Run dictionary transfer fix")
        else:
            print("✅ Auto-learning database state appears healthy")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error investigating database: {e}")
        import traceback
        print(f"📋 Traceback: {traceback.format_exc()}")
        return False

def check_dictionary_loading_mechanism():
    """Check if the dictionary loading mechanism includes auto-learned items"""
    print("\n🔍 CHECKING DICTIONARY LOADING MECHANISM:")
    print("=" * 70)
    
    try:
        # Check the unified_database.js loadDictionary method
        unified_db_path = "core/unified_database.js"
        
        if os.path.exists(unified_db_path):
            with open(unified_db_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check if auto_learned column is being loaded
            if 'auto_learned' in content:
                print("✅ auto_learned column is referenced in unified_database.js")
            else:
                print("❌ auto_learned column is NOT referenced in unified_database.js")
                print("   This could be why auto-learned items don't appear in UI")
            
            # Check the loadDictionary method specifically
            if 'SELECT * FROM dictionary_items' in content:
                print("✅ Dictionary items are being loaded from database")
            else:
                print("❌ Dictionary loading mechanism not found")
        else:
            print("❌ unified_database.js not found")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking dictionary loading: {e}")
        return False

if __name__ == "__main__":
    print("🎯 AUTO-LEARNING DATABASE INVESTIGATION")
    print("=" * 80)
    
    success1 = investigate_auto_learning_database()
    success2 = check_dictionary_loading_mechanism()
    
    overall_success = success1 and success2
    
    if overall_success:
        print(f"\n🎉 INVESTIGATION COMPLETED!")
        print("📋 Check the analysis above for specific issues and recommendations")
    else:
        print(f"\n❌ INVESTIGATION FAILED!")
        print("🔧 Please review the errors above")
    
    sys.exit(0 if overall_success else 1)
