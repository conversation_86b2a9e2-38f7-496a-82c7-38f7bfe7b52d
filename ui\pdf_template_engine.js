/**
 * PDF Document Template Engine
 * Generates PDF documents matching the exact formatting from the Word template
 * Uses jsPDF library for precise PDF generation
 */

class PDFTemplateEngine {
    constructor() {
        this.templateStructure = this.initializeTemplateStructure();
        console.log('📄 PDF Template Engine initialized');
    }

    /**
     * Initialize the exact template structure matching Word template
     */
    initializeTemplateStructure() {
        return {
            pageSettings: {
                format: 'a4',
                orientation: 'portrait',
                margins: {
                    top: 72,    // 1 inch = 72 points
                    right: 72,
                    bottom: 72,
                    left: 72
                }
            },
            fonts: {
                primary: 'times',
                fallback: 'helvetica'
            },
            styles: {
                header: {
                    fontSize: 16,
                    fontStyle: 'bold',
                    align: 'center',
                    marginBottom: 20
                },
                sectionTitle: {
                    fontSize: 12,
                    fontStyle: 'bold',
                    marginTop: 20,
                    marginBottom: 10
                },
                bodyText: {
                    fontSize: 11,
                    fontStyle: 'normal',
                    lineHeight: 1.2
                },
                tableHeader: {
                    fontSize: 11,
                    fontStyle: 'bold',
                    fillColor: [240, 240, 240]
                },
                tableCell: {
                    fontSize: 11,
                    fontStyle: 'normal'
                },
                footer: {
                    fontSize: 9,
                    align: 'center',
                    marginTop: 30
                }
            }
        };
    }

    /**
     * Generate PDF document from smart report data with dynamic content placement
     * @param {Object} smartReport - Smart report data from business rules engine
     * @returns {Object} PDF document data
     */
    async generatePDFDocument(smartReport) {
        console.log('📄 Generating PDF document with dynamic content placement...');

        try {
            // Analyze content for dynamic placement
            const contentAnalysis = this.analyzeContentForPDF(smartReport);
            console.log('📊 PDF Content analysis:', contentAnalysis);

            // Import jsPDF dynamically
            const { jsPDF } = await this.loadJsPDF();

            // Create new PDF document with optimized settings
            const doc = new jsPDF({
                orientation: contentAnalysis.needsLandscape ? 'landscape' : 'portrait',
                unit: 'pt',
                format: 'a4'
            });

            // Set up fonts
            doc.setFont(this.templateStructure.fonts.primary);

            let yPosition = this.templateStructure.pageSettings.margins.top;

            // Generate document content with dynamic placement
            yPosition = this.addHeader(doc, smartReport, yPosition);
            yPosition = this.addInfoTable(doc, smartReport, yPosition);
            yPosition = this.addFindingsSection(doc, smartReport, yPosition, contentAnalysis);
            this.addFooter(doc, smartReport);

            // Generate blob for download
            const pdfBlob = doc.output('blob');
            const url = URL.createObjectURL(pdfBlob);

            // Generate filename
            const period = this.extractPeriodFromMetadata(smartReport.metadata);
            const filename = `Payroll_Audit_Report_${period.month}_${period.year}.pdf`;

            console.log('✅ PDF document generated successfully with dynamic layout');

            return {
                url,
                filename,
                size: pdfBlob.size,
                blob: pdfBlob,
                contentAnalysis
            };

        } catch (error) {
            console.error('❌ Error generating PDF document:', error);
            throw error;
        }
    }

    /**
     * Analyze content for PDF-specific dynamic placement
     * @param {Object} smartReport - Smart report data
     * @returns {Object} Content analysis for PDF optimization
     */
    analyzeContentForPDF(smartReport) {
        const findings = smartReport.findings;
        const allChanges = [
            ...(findings.highPriorityChanges || []),
            ...(findings.moderatePriorityChanges || []),
            ...(findings.lowPriorityChanges || [])
        ];

        // Group by employee
        const employeeGroups = this.groupChangesByEmployee(allChanges);
        const employeeCount = Object.keys(employeeGroups).length;

        // Calculate content density and layout requirements
        const avgChangesPerEmployee = allChanges.length / Math.max(employeeCount, 1);
        const hasWideContent = allChanges.some(change =>
            (change.item_label || '').length > 30 ||
            (change.employee_name || '').length > 25
        );

        // Determine optimal layout
        const needsLandscape = hasWideContent && employeeCount > 10;
        const needsCompactLayout = allChanges.length > 100;
        const estimatedPages = Math.ceil(employeeCount / (needsCompactLayout ? 12 : 8));

        return {
            totalChanges: allChanges.length,
            employeeCount,
            avgChangesPerEmployee,
            hasWideContent,
            needsLandscape,
            needsCompactLayout,
            estimatedPages,
            contentDensity: avgChangesPerEmployee,
            layoutStrategy: this.determineLayoutStrategy(allChanges.length, employeeCount)
        };
    }

    /**
     * Determine optimal layout strategy based on content
     */
    determineLayoutStrategy(totalChanges, employeeCount) {
        if (totalChanges > 200) return 'ultra-compact';
        if (totalChanges > 100) return 'compact';
        if (employeeCount > 20) return 'grouped';
        return 'standard';
    }

    /**
     * Load jsPDF library dynamically
     */
    async loadJsPDF() {
        if (window.jsPDF) {
            return window.jsPDF;
        }
        
        // Load jsPDF from CDN if not available
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js';
            script.onload = () => {
                if (window.jsPDF) {
                    resolve(window.jsPDF);
                } else {
                    reject(new Error('jsPDF failed to load'));
                }
            };
            script.onerror = () => reject(new Error('Failed to load jsPDF library'));
            document.head.appendChild(script);
        });
    }

    /**
     * Add document header
     */
    addHeader(doc, smartReport, yPosition) {
        const period = this.extractPeriodFromMetadata(smartReport.metadata);
        const headerText = `PAYROLL AUDIT REPORT: ${period.month} ${period.year}`;
        
        doc.setFontSize(this.templateStructure.styles.header.fontSize);
        doc.setFont(this.templateStructure.fonts.primary, 'bold');
        
        // Center the text
        const pageWidth = doc.internal.pageSize.getWidth();
        const textWidth = doc.getTextWidth(headerText);
        const xPosition = (pageWidth - textWidth) / 2;
        
        doc.text(headerText, xPosition, yPosition);
        
        return yPosition + this.templateStructure.styles.header.marginBottom;
    }

    /**
     * Add information table
     */
    addInfoTable(doc, smartReport, yPosition) {
        const metadata = smartReport.metadata;
        const summary = smartReport.summary;
        const period = this.extractPeriodFromMetadata(metadata);
        
        // Table data
        const tableData = [
            ['Report Information', 'Executive Summary'],
            [`Period: ${period.month} ${period.year}`, `Significant Changes Detected: ${summary.totalChanges}`],
            [`Generated at: ${this.formatDateTime(metadata.generatedAt)}`, `HIGH Priority Changes: ${summary.highPriorityChanges}`],
            [`Generated By: ${metadata.generatedBy}`, `MODERATE Priority Changes: ${summary.moderatePriorityChanges}`],
            [`Designation: ${metadata.designation}`, `LOW Priority Changes: ${summary.lowPriorityChanges}`]
        ];
        
        // Table settings
        const tableSettings = {
            startY: yPosition,
            head: [tableData[0]],
            body: tableData.slice(1),
            theme: 'grid',
            styles: {
                fontSize: this.templateStructure.styles.tableCell.fontSize,
                font: this.templateStructure.fonts.primary
            },
            headStyles: {
                fillColor: this.templateStructure.styles.tableHeader.fillColor,
                fontStyle: 'bold'
            },
            margin: { 
                left: this.templateStructure.pageSettings.margins.left,
                right: this.templateStructure.pageSettings.margins.right
            }
        };
        
        // Add table using autoTable plugin
        if (doc.autoTable) {
            doc.autoTable(tableSettings);
            return doc.lastAutoTable.finalY + 20;
        } else {
            // Fallback: simple table without autoTable
            return this.addSimpleTable(doc, tableData, yPosition);
        }
    }

    /**
     * Add simple table without autoTable plugin
     */
    addSimpleTable(doc, tableData, yPosition) {
        const cellHeight = 25;
        const cellPadding = 8;
        const pageWidth = doc.internal.pageSize.getWidth();
        const margins = this.templateStructure.pageSettings.margins;
        const tableWidth = pageWidth - margins.left - margins.right;
        const cellWidth = tableWidth / 2;
        
        doc.setFontSize(this.templateStructure.styles.tableCell.fontSize);
        
        tableData.forEach((row, rowIndex) => {
            const currentY = yPosition + (rowIndex * cellHeight);
            
            // Draw cell borders
            doc.rect(margins.left, currentY, cellWidth, cellHeight);
            doc.rect(margins.left + cellWidth, currentY, cellWidth, cellHeight);
            
            // Add cell content
            row.forEach((cell, cellIndex) => {
                const cellX = margins.left + (cellIndex * cellWidth) + cellPadding;
                const cellY = currentY + cellHeight / 2 + 3; // Center vertically
                
                if (rowIndex === 0) {
                    doc.setFont(this.templateStructure.fonts.primary, 'bold');
                } else {
                    doc.setFont(this.templateStructure.fonts.primary, 'normal');
                }
                
                // Wrap text if too long
                const maxWidth = cellWidth - (cellPadding * 2);
                const lines = doc.splitTextToSize(cell, maxWidth);
                doc.text(lines, cellX, cellY);
            });
        });
        
        return yPosition + (tableData.length * cellHeight) + 20;
    }

    /**
     * Add findings and observations section
     */
    addFindingsSection(doc, smartReport, yPosition) {
        // Section title
        doc.setFontSize(this.templateStructure.styles.sectionTitle.fontSize);
        doc.setFont(this.templateStructure.fonts.primary, 'bold');
        doc.text('Finding and Observations', this.templateStructure.pageSettings.margins.left, yPosition);
        
        yPosition += this.templateStructure.styles.sectionTitle.marginBottom + 10;
        
        // Process findings by priority
        const findings = smartReport.findings;
        const priorities = [
            { name: 'HIGH', changes: findings.highPriorityChanges || [] },
            { name: 'MODERATE', changes: findings.moderatePriorityChanges || [] },
            { name: 'LOW', changes: findings.lowPriorityChanges || [] }
        ];
        
        doc.setFontSize(this.templateStructure.styles.bodyText.fontSize);
        
        priorities.forEach(priority => {
            if (priority.changes.length > 0) {
                yPosition = this.addPriorityFindings(doc, priority.changes, yPosition);
            }
        });
        
        return yPosition;
    }

    /**
     * Add priority-specific findings
     */
    addPriorityFindings(doc, changes, yPosition) {
        const margins = this.templateStructure.pageSettings.margins;
        const pageHeight = doc.internal.pageSize.getHeight();
        const maxY = pageHeight - margins.bottom;
        
        // Group changes by employee
        const employeeGroups = this.groupChangesByEmployee(changes);
        
        Object.entries(employeeGroups).forEach(([employeeKey, empChanges]) => {
            const employee = empChanges[0];
            
            // Check if we need a new page
            if (yPosition > maxY - 100) {
                doc.addPage();
                yPosition = margins.top;
            }
            
            // Employee header
            doc.setFont(this.templateStructure.fonts.primary, 'bold');
            const employeeHeader = `${employee.employee_id}: ${employee.employee_name} – ${this.extractDepartment(employee)}`;
            doc.text(employeeHeader, margins.left + 20, yPosition);
            yPosition += 15;
            
            // Employee findings
            doc.setFont(this.templateStructure.fonts.primary, 'normal');
            empChanges.forEach((change, index) => {
                if (yPosition > maxY - 50) {
                    doc.addPage();
                    yPosition = margins.top;
                }
                
                const findingText = `${index + 1}. ${this.formatChangeFinding(change)}`;
                const lines = doc.splitTextToSize(findingText, 400);
                doc.text(lines, margins.left + 40, yPosition);
                yPosition += lines.length * 12 + 5;
            });
            
            yPosition += 10; // Space between employees
        });
        
        return yPosition;
    }

    /**
     * Add footer
     */
    addFooter(doc, smartReport) {
        const period = this.extractPeriodFromMetadata(smartReport.metadata);
        const footerText = `Page 1 | TEMPLAR PAYROLL AUDITOR | All Rights Reserved © ${period.year}`;
        
        doc.setFontSize(this.templateStructure.styles.footer.fontSize);
        doc.setFont(this.templateStructure.fonts.primary, 'normal');
        
        const pageWidth = doc.internal.pageSize.getWidth();
        const pageHeight = doc.internal.pageSize.getHeight();
        const textWidth = doc.getTextWidth(footerText);
        const xPosition = (pageWidth - textWidth) / 2;
        const yPosition = pageHeight - this.templateStructure.pageSettings.margins.bottom + 20;
        
        // Add border line above footer
        doc.line(
            this.templateStructure.pageSettings.margins.left,
            yPosition - 15,
            pageWidth - this.templateStructure.pageSettings.margins.right,
            yPosition - 15
        );
        
        doc.text(footerText, xPosition, yPosition);
    }

    // HELPER METHODS (reusing from Word Template Engine)
    
    extractPeriodFromMetadata(metadata) {
        const date = new Date(metadata.generatedAt);
        return {
            month: date.toLocaleString('default', { month: 'long' }).toUpperCase(),
            year: date.getFullYear()
        };
    }

    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('en-GB', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    groupChangesByEmployee(changes) {
        return changes.reduce((groups, change) => {
            const key = `${change.employee_id}-${change.employee_name}`;
            if (!groups[key]) groups[key] = [];
            groups[key].push(change);
            return groups;
        }, {});
    }

    extractDepartment(employee) {
        const sectionName = employee.section_name || '';
        if (sectionName.toLowerCase().includes('minister')) return 'MINISTERS';
        if (sectionName.toLowerCase().includes('staff')) return 'STAFF';
        return 'GENERAL';
    }

    formatChangeFinding(change) {
        const itemName = change.item_name || change.item_label;
        const prevValue = this.formatValue(change.previous_value);
        const currValue = this.formatValue(change.current_value);
        const changeType = change.category || change.change_type;
        
        switch (changeType?.toUpperCase()) {
            case 'INCREASE':
                return `${itemName} increased from ${prevValue} to ${currValue} in ${this.getCurrentPeriod()} (increase of ${this.calculateDifference(change.previous_value, change.current_value)})`;
            case 'DECREASE':
                return `${itemName} decreased from ${prevValue} to ${currValue} in ${this.getCurrentPeriod()} (decrease of ${this.calculateDifference(change.previous_value, change.current_value)})`;
            default:
                return `${itemName} changed from ${prevValue} to ${currValue} in ${this.getCurrentPeriod()}`;
        }
    }

    formatValue(value) {
        if (value === null || value === undefined || value === '') return '0.00';
        const numValue = parseFloat(value);
        if (isNaN(numValue)) return value;
        return numValue.toFixed(2);
    }

    calculateDifference(prevValue, currValue) {
        const prev = parseFloat(prevValue) || 0;
        const curr = parseFloat(currValue) || 0;
        return Math.abs(curr - prev).toFixed(2);
    }

    getCurrentPeriod() {
        const date = new Date();
        return `${date.toLocaleString('default', { month: 'long' })} ${date.getFullYear()}`;
    }
}

// Export for use in Final Report Interface
window.PDFTemplateEngine = PDFTemplateEngine;
