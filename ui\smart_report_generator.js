/**
 * Smart Report Generator
 * Generates intelligent reports using business rules processing results
 */

class SmartReportGenerator {
    constructor() {
        this.templates = {
            employeeBased: this.getEmployeeBasedTemplate(),
            itemBased: this.getItemBasedTemplate()
        };
        
        console.log('📄 Smart Report Generator initialized');
    }

    /**
     * Generate smart report based on processed data
     * @param {Object} reportData - Processed report data from business rules
     * @returns {Object} Generated report structure
     */
    generateReport(reportData) {
        console.log('📄 Generating smart report...');
        
        const reportType = reportData.metadata.reportType;
        const template = this.templates[reportType] || this.templates.employeeBased;
        
        const report = {
            metadata: reportData.metadata,
            executiveSummary: this.generateExecutiveSummary(reportData),
            sections: this.generateReportSections(reportData, template),
            appendices: this.generateAppendices(reportData),
            recommendations: this.generateRecommendations(reportData)
        };
        
        console.log('✅ Smart report generated:', report);
        return report;
    }

    /**
     * Generate executive summary
     */
    generateExecutiveSummary(reportData) {
        const summary = reportData.summary;
        const findings = reportData.findings;
        
        return {
            title: 'Executive Summary',
            content: [
                `This payroll audit report presents findings from the analysis of ${summary.totalChanges} payroll changes.`,
                `The analysis identified ${summary.highPriorityChanges} high-priority changes, ${summary.moderatePriorityChanges} moderate-priority changes, and ${summary.lowPriorityChanges} low-priority changes.`,
                summary.promotionsDetected > 0 ? `${summary.promotionsDetected} promotions were detected during the review period.` : null,
                summary.transfersDetected > 0 ? `${summary.transfersDetected} transfers were identified.` : null,
                `${summary.bulkChangesCount} bulk change patterns were analyzed for compliance and accuracy.`
            ].filter(Boolean),
            keyMetrics: {
                totalChanges: summary.totalChanges,
                highPriorityChanges: summary.highPriorityChanges,
                promotions: summary.promotionsDetected,
                transfers: summary.transfersDetected,
                bulkChanges: summary.bulkChangesCount
            }
        };
    }

    /**
     * Generate report sections based on template
     */
    generateReportSections(reportData, template) {
        const sections = [];
        
        template.sections.forEach(sectionConfig => {
            switch(sectionConfig.type) {
                case 'findings':
                    sections.push(this.generateFindingsSection(reportData));
                    break;
                case 'promotions':
                    if (reportData.specialFindings.promotions.length > 0) {
                        sections.push(this.generatePromotionsSection(reportData.specialFindings.promotions));
                    }
                    break;
                case 'transfers':
                    if (reportData.specialFindings.transfers.length > 0) {
                        sections.push(this.generateTransfersSection(reportData.specialFindings.transfers));
                    }
                    break;
                case 'bulk_changes':
                    sections.push(this.generateBulkChangesSection(reportData.specialFindings.bulkChanges));
                    break;
                case 'new_employees':
                    sections.push(this.generateNewEmployeesSection(reportData));
                    break;
                case 'removed_employees':
                    sections.push(this.generateRemovedEmployeesSection(reportData));
                    break;
            }
        });
        
        return sections;
    }

    /**
     * Generate findings section
     */
    generateFindingsSection(reportData) {
        const findings = reportData.findings;
        
        return {
            title: 'Detailed Findings',
            subsections: [
                {
                    title: 'High Priority Changes',
                    count: findings.highPriorityChanges.length,
                    changes: findings.highPriorityChanges,
                    description: 'Changes requiring immediate attention and review'
                },
                {
                    title: 'Moderate Priority Changes', 
                    count: findings.moderatePriorityChanges.length,
                    changes: findings.moderatePriorityChanges,
                    description: 'Changes requiring standard review procedures'
                },
                {
                    title: 'Low Priority Changes',
                    count: findings.lowPriorityChanges.length,
                    changes: findings.lowPriorityChanges,
                    description: 'Routine changes for documentation purposes'
                }
            ]
        };
    }

    /**
     * Generate promotions section
     */
    generatePromotionsSection(promotions) {
        return {
            title: 'Promotions and Salary Adjustments',
            count: promotions.length,
            content: [
                `${promotions.length} promotion(s) were identified during the review period.`,
                'The following employees received promotions or significant salary adjustments:'
            ],
            items: promotions.map(promotion => ({
                employeeName: promotion.employeeName,
                employeeId: promotion.employeeId,
                salaryIncrease: promotion.salaryIncrease,
                type: promotion.type,
                details: `Salary increase of ${this.formatCurrency(promotion.salaryIncrease)}`
            }))
        };
    }

    /**
     * Generate transfers section
     */
    generateTransfersSection(transfers) {
        return {
            title: 'Employee Transfers',
            count: transfers.length,
            content: [
                `${transfers.length} employee transfer(s) were processed during the review period.`,
                'The following transfers were identified:'
            ],
            items: transfers.map(transfer => ({
                employeeName: transfer.employeeName,
                employeeId: transfer.employeeId,
                type: transfer.type,
                details: 'Department/location change detected'
            }))
        };
    }

    /**
     * Generate bulk changes section
     */
    generateBulkChangesSection(bulkChanges) {
        const significantBulkChanges = Object.entries(bulkChanges)
            .filter(([type, data]) => data.isSignificant);
        
        return {
            title: 'Bulk Changes Analysis',
            count: Object.keys(bulkChanges).length,
            content: [
                `${Object.keys(bulkChanges).length} bulk change pattern(s) were analyzed.`,
                `${significantBulkChanges.length} pattern(s) were classified as significant.`
            ],
            items: Object.entries(bulkChanges).map(([type, data]) => ({
                changeType: type,
                count: data.count,
                category: data.category,
                isSignificant: data.isSignificant,
                details: `${data.count} employees affected - ${data.category}`
            }))
        };
    }

    /**
     * Generate new employees section
     */
    generateNewEmployeesSection(reportData) {
        const newEmployees = reportData.processedChanges.filter(change => 
            change.category === 'NEW' && 
            change.section_name?.toLowerCase().includes('personal')
        );
        
        return {
            title: 'New Employees',
            count: newEmployees.length,
            content: newEmployees.length > 0 ? 
                [`${newEmployees.length} new employee(s) were added to the payroll.`] :
                ['No new employees were added during this period.'],
            items: newEmployees.map(emp => ({
                employeeName: emp.employee_name,
                employeeId: emp.employee_id,
                details: 'New employee added to payroll'
            }))
        };
    }

    /**
     * Generate removed employees section
     */
    generateRemovedEmployeesSection(reportData) {
        const removedEmployees = reportData.processedChanges.filter(change => 
            change.category === 'REMOVED' && 
            change.section_name?.toLowerCase().includes('personal')
        );
        
        return {
            title: 'Removed Employees',
            count: removedEmployees.length,
            content: removedEmployees.length > 0 ? 
                [`${removedEmployees.length} employee(s) were removed from the payroll.`] :
                ['No employees were removed during this period.'],
            items: removedEmployees.map(emp => ({
                employeeName: emp.employee_name,
                employeeId: emp.employee_id,
                details: 'Employee removed from payroll'
            }))
        };
    }

    /**
     * Generate appendices
     */
    generateAppendices(reportData) {
        return [
            {
                title: 'Appendix A: Complete Change Log',
                content: 'Detailed listing of all payroll changes',
                data: reportData.processedChanges
            },
            {
                title: 'Appendix B: Business Rules Applied',
                content: 'Summary of business rules and criteria used in this analysis',
                data: {
                    priorityRules: 'Changes categorized by section importance',
                    bulkAnalysis: 'Bulk changes analyzed for patterns and compliance',
                    promotionDetection: 'Automatic detection of promotions and salary adjustments',
                    transferDetection: 'Identification of employee transfers and relocations'
                }
            }
        ];
    }

    /**
     * Generate recommendations
     */
    generateRecommendations(reportData) {
        const recommendations = [];
        
        if (reportData.summary.highPriorityChanges > 0) {
            recommendations.push({
                priority: 'HIGH',
                title: 'Review High Priority Changes',
                description: `${reportData.summary.highPriorityChanges} high-priority changes require immediate review and verification.`
            });
        }
        
        if (reportData.summary.promotionsDetected > 0) {
            recommendations.push({
                priority: 'MODERATE',
                title: 'Verify Promotion Documentation',
                description: `${reportData.summary.promotionsDetected} promotions detected. Ensure proper documentation and approval processes were followed.`
            });
        }
        
        if (reportData.summary.bulkChangesCount > 5) {
            recommendations.push({
                priority: 'MODERATE',
                title: 'Review Bulk Change Procedures',
                description: `${reportData.summary.bulkChangesCount} bulk change patterns identified. Consider reviewing bulk change procedures for efficiency.`
            });
        }
        
        return recommendations;
    }

    /**
     * Get employee-based report template
     */
    getEmployeeBasedTemplate() {
        return {
            groupBy: 'employee',
            sortBy: 'employee_name',
            sections: [
                { type: 'findings', required: true },
                { type: 'promotions', required: false },
                { type: 'transfers', required: false },
                { type: 'new_employees', required: false },
                { type: 'removed_employees', required: false }
            ],
            includeAppendix: true
        };
    }

    /**
     * Get item-based report template
     */
    getItemBasedTemplate() {
        return {
            groupBy: 'item_type',
            sortBy: 'section_name',
            sections: [
                { type: 'findings', required: true },
                { type: 'bulk_changes', required: true }
            ],
            includeAppendix: false
        };
    }

    /**
     * Format currency values
     */
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-GH', {
            style: 'currency',
            currency: 'GHS'
        }).format(amount);
    }
}

// Export for use in Final Report Interface
window.SmartReportGenerator = SmartReportGenerator;
