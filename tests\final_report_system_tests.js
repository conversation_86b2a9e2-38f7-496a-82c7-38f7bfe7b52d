/**
 * FINAL REPORT SYSTEM COMPREHENSIVE TESTS
 * Tests all components of the enhanced Final Report system
 * Verifies specification compliance and real data processing
 */

class FinalReportSystemTests {
    constructor() {
        this.testResults = [];
        this.testData = null;
        this.businessRulesEngine = null;
        this.employeeReportGenerator = null;
        this.itemReportGenerator = null;
        this.wordTemplateEngine = null;
        this.appendixGenerator = null;
        this.dualConfiguration = null;
        this.reportDistinctionManager = null;

        console.log('🧪 Final Report System Tests initialized');
    }

    /**
     * Initialize test environment
     */
    async initializeTestEnvironment() {
        console.log('🔧 Initializing test environment...');

        try {
            // Initialize all system components
            this.businessRulesEngine = new BusinessRulesEngine();
            this.employeeReportGenerator = new EmployeeBasedReportGenerator();
            this.itemReportGenerator = new ItemBasedReportGenerator();
            this.wordTemplateEngine = new WordTemplateEngine();
            this.appendixGenerator = new AppendixGenerator();
            this.dualConfiguration = new DualReportConfiguration();
            this.reportDistinctionManager = new ReportDistinctionManager();

            // Load test data
            this.testData = await this.loadTestData();

            console.log('✅ Test environment initialized successfully');
            return true;

        } catch (error) {
            console.error('❌ Failed to initialize test environment:', error);
            return false;
        }
    }

    /**
     * Load test data for verification
     */
    async loadTestData() {
        // Mock test data that represents real payroll changes
        return {
            currentPayrollData: [
                {
                    employee_id: 'COP2626',
                    employee_name: 'SAMUEL ASIEDU',
                    department: 'AUDIT, MONITORING & EVALUATION',
                    section_name: 'Deductions',
                    item_name: 'RESPONSIBILITY HEADS',
                    previous_value: '2000',
                    current_value: '1500',
                    change_type: 'DECREASED'
                },
                {
                    employee_id: 'COP2524',
                    employee_name: 'MARK ANTHONY',
                    department: 'FINANCE',
                    section_name: 'Allowances',
                    item_name: 'SECURITY GUARD ALLOWANCE',
                    previous_value: '0',
                    current_value: '800',
                    change_type: 'NEW'
                },
                {
                    employee_id: 'COP4040',
                    employee_name: 'EBENEZER ARHIN',
                    department: 'SECRETARIAT',
                    section_name: 'Allowances',
                    item_name: 'NEW LAND ALLOWANCE',
                    previous_value: '0',
                    current_value: '700',
                    change_type: 'NEW'
                }
            ],
            previousPayrollData: [
                {
                    employee_id: 'COP2626',
                    employee_name: 'SAMUEL ASIEDU',
                    department: 'AUDIT, MONITORING & EVALUATION',
                    section_name: 'Deductions',
                    item_name: 'RESPONSIBILITY HEADS',
                    previous_value: '2000',
                    current_value: '2000'
                },
                {
                    employee_id: 'COP1513',
                    employee_name: 'REMOVED EMPLOYEE',
                    department: 'ABUAKWA AREA - MINISTERS',
                    section_name: 'Earnings',
                    item_name: 'BASIC SALARY',
                    previous_value: '5000',
                    current_value: '5000'
                }
            ],
            expectedResults: {
                newEmployees: ['COP2524', 'COP4040'],
                removedEmployees: ['COP1513'],
                totalChanges: 3,
                highPriorityChanges: 0,
                moderatePriorityChanges: 1,
                lowPriorityChanges: 2
            }
        };
    }

    /**
     * Run all tests
     */
    async runAllTests() {
        console.log('🚀 Starting comprehensive Final Report System tests...');

        const initialized = await this.initializeTestEnvironment();
        if (!initialized) {
            console.error('❌ Cannot run tests - initialization failed');
            return false;
        }

        // Test suite
        await this.testBusinessRulesEngine();
        await this.testEmployeeBasedReportGenerator();
        await this.testItemBasedReportGenerator();
        await this.testWordTemplateEngine();
        await this.testAppendixGenerator();
        await this.testDualConfiguration();
        await this.testReportDistinction();
        await this.testIntegrationWorkflow();
        await this.testSpecificationCompliance();

        // Generate test report
        this.generateTestReport();

        return this.getTestSummary();
    }

    /**
     * Test Business Rules Engine
     */
    async testBusinessRulesEngine() {
        console.log('🧠 Testing Business Rules Engine...');

        try {
            // Test intelligent analysis
            const results = this.businessRulesEngine.processChanges(
                this.testData.currentPayrollData,
                this.testData.previousPayrollData
            );

            // Verify results structure
            this.addTestResult('Business Rules Engine - Structure',
                results.hasOwnProperty('analyzedChanges') &&
                results.hasOwnProperty('employeeBasedData') &&
                results.hasOwnProperty('newEmployees') &&
                results.hasOwnProperty('removedEmployees'),
                'Results contain all required sections'
            );

            // Test NEW employee detection
            this.addTestResult('NEW Employee Detection',
                results.newEmployees && results.newEmployees.length === 2,
                `Expected 2 new employees, got ${results.newEmployees?.length || 0}`
            );

            // Test REMOVED employee detection
            this.addTestResult('REMOVED Employee Detection',
                results.removedEmployees && results.removedEmployees.length === 1,
                `Expected 1 removed employee, got ${results.removedEmployees?.length || 0}`
            );

            // Test change type classification
            const decreaseChanges = results.analyzedChanges.filter(c => c.changeType === 'DECREASED');
            this.addTestResult('Change Type Classification',
                decreaseChanges.length === 1,
                `Expected 1 DECREASED change, got ${decreaseChanges.length}`
            );

            // Test narration generation
            const hasNarration = results.analyzedChanges.every(c => c.narration !== null);
            this.addTestResult('Narration Generation',
                hasNarration,
                'All changes have proper narration'
            );

        } catch (error) {
            this.addTestResult('Business Rules Engine', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test Employee-Based Report Generator
     */
    async testEmployeeBasedReportGenerator() {
        console.log('👥 Testing Employee-Based Report Generator...');

        try {
            // Process data with business rules first
            const intelligentResults = this.businessRulesEngine.processChanges(
                this.testData.currentPayrollData,
                this.testData.previousPayrollData
            );

            const config = { generatedBy: 'Test User', designation: 'Test Officer' };
            const reportData = this.employeeReportGenerator.generateEmployeeBasedReport(intelligentResults, config);

            // Test report structure
            this.addTestResult('Employee Report - Structure',
                reportData.hasOwnProperty('metadata') &&
                reportData.hasOwnProperty('findingsAndObservations') &&
                reportData.hasOwnProperty('newEmployees') &&
                reportData.hasOwnProperty('removedEmployees'),
                'Report contains all required sections'
            );

            // Test metadata
            this.addTestResult('Employee Report - Metadata',
                reportData.metadata.generatedBy === 'Test User' &&
                reportData.metadata.designation === 'Test Officer',
                'Metadata correctly inherited from configuration'
            );

            // Test findings format
            const hasEmployeeFindings = reportData.findingsAndObservations.length > 0;
            this.addTestResult('Employee Report - Findings',
                hasEmployeeFindings,
                'Employee findings generated correctly'
            );

            // Test NEW employees section
            this.addTestResult('Employee Report - NEW Employees',
                reportData.newEmployees === null || reportData.newEmployees.employees.length >= 0,
                'NEW employees section handled correctly'
            );

        } catch (error) {
            this.addTestResult('Employee-Based Report Generator', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test Item-Based Report Generator
     */
    async testItemBasedReportGenerator() {
        console.log('📋 Testing Item-Based Report Generator...');

        try {
            const intelligentResults = this.businessRulesEngine.processChanges(
                this.testData.currentPayrollData,
                this.testData.previousPayrollData
            );

            const config = { generatedBy: 'Test User', designation: 'Test Officer' };
            const reportData = this.itemReportGenerator.generateItemBasedReport(intelligentResults, config);

            // Test report structure
            this.addTestResult('Item Report - Structure',
                reportData.hasOwnProperty('metadata') &&
                reportData.hasOwnProperty('itemFindings'),
                'Report contains required sections'
            );

            // Test item findings format
            const hasItemFindings = reportData.itemFindings.length > 0;
            this.addTestResult('Item Report - Findings',
                hasItemFindings,
                'Item findings generated correctly'
            );

            // Test item format compliance
            if (reportData.itemFindings.length > 0) {
                const firstItem = reportData.itemFindings[0];
                const hasCorrectFormat = firstItem.hasOwnProperty('affectedEmployees') &&
                                       firstItem.hasOwnProperty('narration');
                this.addTestResult('Item Report - Format',
                    hasCorrectFormat,
                    'Item format follows specification'
                );
            }

        } catch (error) {
            this.addTestResult('Item-Based Report Generator', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test Word Template Engine
     */
    async testWordTemplateEngine() {
        console.log('📄 Testing Word Template Engine...');

        try {
            const templateSpecs = this.wordTemplateEngine.getTemplateSpecs();

            // Test template specifications
            this.addTestResult('Word Template - Font Specs',
                templateSpecs.fonts.body.name === 'Cambria (Body)' &&
                templateSpecs.fonts.body.size === 14 &&
                templateSpecs.fonts.heading.name === 'Calibri (Headings)' &&
                templateSpecs.fonts.heading.size === 26,
                'Font specifications match requirements'
            );

            // Test document structure
            this.addTestResult('Word Template - Structure',
                templateSpecs.hasOwnProperty('pageLayout') &&
                templateSpecs.hasOwnProperty('sections'),
                'Template structure is properly defined'
            );

        } catch (error) {
            this.addTestResult('Word Template Engine', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test Appendix Generator
     */
    async testAppendixGenerator() {
        console.log('📋 Testing Appendix Generator...');

        try {
            // Mock promotion and transfer data
            const mockResults = {
                promotions: {
                    ministers: [{
                        employeeId: 'COP001',
                        employeeName: 'Test Minister',
                        department: 'MINISTERS',
                        oldTitle: 'Pastor',
                        newTitle: 'Apostle'
                    }],
                    staff: [{
                        employeeId: 'COP002',
                        employeeName: 'Test Staff',
                        department: 'FINANCE',
                        oldTitle: 'Officer',
                        newTitle: 'Senior Officer'
                    }]
                },
                transfers: {
                    ministers: [],
                    staff: [{
                        employeeId: 'COP003',
                        employeeName: 'Test Transfer',
                        department: 'HR',
                        oldDepartment: 'FINANCE',
                        newDepartment: 'HR'
                    }]
                }
            };

            const appendix = this.appendixGenerator.generateAppendix(mockResults);

            // Test appendix generation
            this.addTestResult('Appendix - Generation',
                Array.isArray(appendix) && appendix.length > 0,
                'Appendix sections generated successfully'
            );

            // Test section ordering
            const sectionTitles = appendix.map(section => section.title);
            const expectedOrder = ['PROMOTIONS MINISTERS', 'PROMOTIONS STAFF', 'TRANSFERS STAFF'];
            const correctOrder = expectedOrder.every(title => sectionTitles.includes(title));

            this.addTestResult('Appendix - Section Order',
                correctOrder,
                'Appendix sections in correct order'
            );

        } catch (error) {
            this.addTestResult('Appendix Generator', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test Dual Configuration
     */
    async testDualConfiguration() {
        console.log('⚙️ Testing Dual Configuration...');

        try {
            // Test configuration validation
            const config = this.dualConfiguration.getConfiguration();
            this.addTestResult('Dual Config - Initial State',
                config.hasOwnProperty('generatedBy') &&
                config.hasOwnProperty('designation'),
                'Configuration has required fields'
            );

            // Test configuration update
            this.dualConfiguration.setConfiguration({
                generatedBy: 'Test User',
                designation: 'Test Officer'
            });

            const updatedConfig = this.dualConfiguration.getConfiguration();
            this.addTestResult('Dual Config - Update',
                updatedConfig.generatedBy === 'Test User' &&
                updatedConfig.designation === 'Test Officer',
                'Configuration updates correctly'
            );

            // Test validation
            const isValid = this.dualConfiguration.isValidForReportGeneration();
            this.addTestResult('Dual Config - Validation',
                isValid,
                'Configuration validation works correctly'
            );

        } catch (error) {
            this.addTestResult('Dual Configuration', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test Report Distinction
     */
    async testReportDistinction() {
        console.log('🔄 Testing Report Distinction...');

        try {
            // Test mode switching
            const preReportMode = this.reportDistinctionManager.switchMode('pre-reporting');
            this.addTestResult('Report Distinction - Pre-Report Mode',
                preReportMode && this.reportDistinctionManager.getCurrentMode() === 'pre-reporting',
                'Pre-reporting mode switch works'
            );

            const smartReporterMode = this.reportDistinctionManager.switchMode('smart-reporter');
            this.addTestResult('Report Distinction - Smart Reporter Mode',
                smartReporterMode && this.reportDistinctionManager.getCurrentMode() === 'smart-reporter',
                'Smart reporter mode switch works'
            );

            // Test capability comparison
            const capabilities = this.reportDistinctionManager.getCapabilityComparison();
            this.addTestResult('Report Distinction - Capabilities',
                capabilities.hasOwnProperty('preReporting') &&
                capabilities.hasOwnProperty('smartReporter') &&
                capabilities.hasOwnProperty('differences'),
                'Capability comparison available'
            );

        } catch (error) {
            this.addTestResult('Report Distinction', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test Integration Workflow
     */
    async testIntegrationWorkflow() {
        console.log('🔗 Testing Integration Workflow...');

        try {
            // Test complete workflow
            const intelligentResults = this.businessRulesEngine.processChanges(
                this.testData.currentPayrollData,
                this.testData.previousPayrollData
            );

            const config = { generatedBy: 'Integration Test', designation: 'Test Officer' };

            // Generate both report types
            const employeeReport = this.employeeReportGenerator.generateEmployeeBasedReport(intelligentResults, config);
            const itemReport = this.itemReportGenerator.generateItemBasedReport(intelligentResults, config);

            // Generate appendix
            const appendix = this.appendixGenerator.generateAppendix(intelligentResults);

            this.addTestResult('Integration - Complete Workflow',
                employeeReport && itemReport && appendix,
                'Complete workflow executes successfully'
            );

            // Test data consistency
            const employeeMetadata = employeeReport.metadata;
            const itemMetadata = itemReport.metadata;

            this.addTestResult('Integration - Data Consistency',
                employeeMetadata.generatedBy === itemMetadata.generatedBy &&
                employeeMetadata.designation === itemMetadata.designation,
                'Data consistency maintained across components'
            );

        } catch (error) {
            this.addTestResult('Integration Workflow', false, `Error: ${error.message}`);
        }
    }

    /**
     * Test Specification Compliance
     */
    async testSpecificationCompliance() {
        console.log('📋 Testing Specification Compliance...');

        try {
            // Test NEW/REMOVED employee detection compliance
            const intelligentResults = this.businessRulesEngine.processChanges(
                this.testData.currentPayrollData,
                this.testData.previousPayrollData
            );

            this.addTestResult('Spec Compliance - NEW Employees',
                intelligentResults.newEmployees && intelligentResults.newEmployees.length > 0,
                'NEW employee detection per specification'
            );

            this.addTestResult('Spec Compliance - REMOVED Employees',
                intelligentResults.removedEmployees && intelligentResults.removedEmployees.length > 0,
                'REMOVED employee detection per specification'
            );

            // Test report format compliance
            const config = { generatedBy: 'Spec Test', designation: 'Test Officer' };
            const employeeReport = this.employeeReportGenerator.generateEmployeeBasedReport(intelligentResults, config);

            this.addTestResult('Spec Compliance - Report Format',
                employeeReport.metadata.title.includes('PAYROLL AUDIT REPORT:') &&
                employeeReport.reportInformation['Generated By'] === 'Spec Test',
                'Report format matches specification'
            );

            // Test font specifications
            const templateSpecs = this.wordTemplateEngine.getTemplateSpecs();
            this.addTestResult('Spec Compliance - Fonts',
                templateSpecs.fonts.body.name === 'Cambria (Body)' &&
                templateSpecs.fonts.body.size === 14 &&
                templateSpecs.fonts.heading.name === 'Calibri (Headings)' &&
                templateSpecs.fonts.heading.size === 26,
                'Font specifications match requirements'
            );

        } catch (error) {
            this.addTestResult('Specification Compliance', false, `Error: ${error.message}`);
        }
    }

    /**
     * Add test result
     */
    addTestResult(testName, passed, details) {
        this.testResults.push({
            name: testName,
            status: passed ? 'PASSED' : 'FAILED',
            details: details,
            timestamp: new Date().toISOString()
        });

        const status = passed ? '✅' : '❌';
        console.log(`${status} ${testName}: ${details}`);
    }

    /**
     * Generate test report
     */
    generateTestReport() {
        const passed = this.testResults.filter(r => r.status === 'PASSED').length;
        const failed = this.testResults.filter(r => r.status === 'FAILED').length;
        const total = this.testResults.length;

        console.log('\n📊 FINAL REPORT SYSTEM TEST RESULTS:');
        console.log('='.repeat(50));
        console.log(`Total Tests: ${total}`);
        console.log(`✅ Passed: ${passed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`Success Rate: ${((passed / total) * 100).toFixed(1)}%`);
        console.log('='.repeat(50));

        // Show failed tests
        const failedTests = this.testResults.filter(r => r.status === 'FAILED');
        if (failedTests.length > 0) {
            console.log('\n❌ FAILED TESTS:');
            failedTests.forEach(test => {
                console.log(`- ${test.name}: ${test.details}`);
            });
        }

        return {
            total,
            passed,
            failed,
            successRate: ((passed / total) * 100).toFixed(1),
            results: this.testResults
        };
    }

    /**
     * Get test summary
     */
    getTestSummary() {
        const passed = this.testResults.filter(r => r.status === 'PASSED').length;
        const total = this.testResults.length;

        return {
            success: passed === total,
            passed,
            total,
            successRate: ((passed / total) * 100).toFixed(1),
            results: this.testResults
        };
    }
}

// Export for use
window.FinalReportSystemTests = FinalReportSystemTests;