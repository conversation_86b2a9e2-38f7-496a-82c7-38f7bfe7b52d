/**
 * Excel Document Template Engine
 * Generates Excel spreadsheets with tabular data presentation optimized for analysis
 * Uses SheetJS library for Excel generation
 */

class ExcelTemplateEngine {
    constructor() {
        this.templateStructure = this.initializeTemplateStructure();
        console.log('📊 Excel Template Engine initialized');
    }

    /**
     * Initialize the Excel template structure
     */
    initializeTemplateStructure() {
        return {
            workbook: {
                name: 'Payroll Audit Report',
                sheets: {
                    summary: 'Executive Summary',
                    findings: 'Detailed Findings',
                    employees: 'Employee Analysis',
                    statistics: 'Statistics'
                }
            },
            styles: {
                header: {
                    font: { bold: true, size: 14 },
                    alignment: { horizontal: 'center' },
                    fill: { fgColor: { rgb: '366092' } },
                    font: { color: { rgb: 'FFFFFF' }, bold: true }
                },
                subHeader: {
                    font: { bold: true, size: 12 },
                    fill: { fgColor: { rgb: 'D9E2F3' } }
                },
                tableHeader: {
                    font: { bold: true },
                    fill: { fgColor: { rgb: 'F2F2F2' } },
                    border: {
                        top: { style: 'thin' },
                        bottom: { style: 'thin' },
                        left: { style: 'thin' },
                        right: { style: 'thin' }
                    }
                },
                dataCell: {
                    border: {
                        top: { style: 'thin' },
                        bottom: { style: 'thin' },
                        left: { style: 'thin' },
                        right: { style: 'thin' }
                    }
                },
                priorityHigh: {
                    fill: { fgColor: { rgb: 'FFE6E6' } }
                },
                priorityMedium: {
                    fill: { fgColor: { rgb: 'FFF2E6' } }
                },
                priorityLow: {
                    fill: { fgColor: { rgb: 'E6F7E6' } }
                }
            }
        };
    }

    /**
     * Generate Excel document from smart report data with dynamic content placement
     * @param {Object} smartReport - Smart report data from business rules engine
     * @returns {Object} Excel document data
     */
    async generateExcelDocument(smartReport) {
        console.log('📊 Generating Excel document with dynamic content placement...');

        try {
            // Analyze content for optimal Excel layout
            const contentAnalysis = this.analyzeContentForExcel(smartReport);
            console.log('📊 Excel Content analysis:', contentAnalysis);

            // Load SheetJS library
            const XLSX = await this.loadSheetJS();

            // Create new workbook
            const workbook = XLSX.utils.book_new();

            // Generate sheets with dynamic content placement
            this.addSummarySheet(workbook, XLSX, smartReport, contentAnalysis);
            this.addFindingsSheet(workbook, XLSX, smartReport, contentAnalysis);
            this.addEmployeeAnalysisSheet(workbook, XLSX, smartReport, contentAnalysis);
            this.addStatisticsSheet(workbook, XLSX, smartReport, contentAnalysis);

            // Add additional sheets if needed for large datasets
            if (contentAnalysis.needsAdditionalSheets) {
                this.addDetailedBreakdownSheets(workbook, XLSX, smartReport, contentAnalysis);
            }

            // Generate blob for download
            const excelBuffer = XLSX.write(workbook, {
                bookType: 'xlsx',
                type: 'array',
                cellStyles: true
            });

            const blob = new Blob([excelBuffer], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            });

            const url = URL.createObjectURL(blob);

            // Generate filename
            const period = this.extractPeriodFromMetadata(smartReport.metadata);
            const filename = `Payroll_Audit_Report_${period.month}_${period.year}.xlsx`;

            console.log('✅ Excel document generated successfully with optimized layout');

            return {
                url,
                filename,
                size: blob.size,
                blob: blob,
                contentAnalysis
            };

        } catch (error) {
            console.error('❌ Error generating Excel document:', error);
            throw error;
        }
    }

    /**
     * Analyze content for Excel-specific dynamic placement
     * @param {Object} smartReport - Smart report data
     * @returns {Object} Content analysis for Excel optimization
     */
    analyzeContentForExcel(smartReport) {
        const findings = smartReport.findings;
        const allChanges = [
            ...(findings.highPriorityChanges || []),
            ...(findings.moderatePriorityChanges || []),
            ...(findings.lowPriorityChanges || [])
        ];

        // Analyze data characteristics
        const employeeGroups = this.groupChangesByEmployee(allChanges);
        const employeeCount = Object.keys(employeeGroups).length;
        const maxChangesPerEmployee = Math.max(...Object.values(employeeGroups).map(group => group.length));

        // Determine optimal Excel layout
        const needsAdditionalSheets = allChanges.length > 1000 || employeeCount > 100;
        const needsWideColumns = allChanges.some(change =>
            (change.item_label || '').length > 25 ||
            (change.employee_name || '').length > 20
        );

        // Calculate optimal column widths
        const columnWidths = this.calculateOptimalColumnWidths(allChanges);

        return {
            totalChanges: allChanges.length,
            employeeCount,
            maxChangesPerEmployee,
            needsAdditionalSheets,
            needsWideColumns,
            columnWidths,
            dataComplexity: this.assessDataComplexity(allChanges),
            sheetStrategy: this.determineSheetStrategy(allChanges.length, employeeCount)
        };
    }

    /**
     * Calculate optimal column widths based on content
     */
    calculateOptimalColumnWidths(changes) {
        const maxLengths = {
            employeeId: Math.max(12, ...changes.map(c => (c.employee_id || '').length)),
            employeeName: Math.max(25, ...changes.map(c => (c.employee_name || '').length)),
            section: Math.max(20, ...changes.map(c => (c.section_name || '').length)),
            item: Math.max(20, ...changes.map(c => (c.item_label || '').length)),
            description: Math.max(50, ...changes.map(c => this.formatChangeFinding(c).length))
        };

        return {
            employeeId: Math.min(maxLengths.employeeId + 2, 15),
            employeeName: Math.min(maxLengths.employeeName + 2, 30),
            section: Math.min(maxLengths.section + 2, 25),
            item: Math.min(maxLengths.item + 2, 25),
            description: Math.min(maxLengths.description + 2, 60)
        };
    }

    /**
     * Assess data complexity for layout decisions
     */
    assessDataComplexity(changes) {
        const uniqueSections = new Set(changes.map(c => c.section_name)).size;
        const uniqueItems = new Set(changes.map(c => c.item_label)).size;
        const changeTypes = new Set(changes.map(c => c.change_type)).size;

        if (uniqueSections > 10 && uniqueItems > 50) return 'high';
        if (uniqueSections > 5 && uniqueItems > 20) return 'medium';
        return 'low';
    }

    /**
     * Determine optimal sheet organization strategy
     */
    determineSheetStrategy(totalChanges, employeeCount) {
        if (totalChanges > 2000) return 'multi-sheet-by-priority';
        if (employeeCount > 150) return 'multi-sheet-by-section';
        if (totalChanges > 500) return 'single-sheet-with-filters';
        return 'standard';
    }

    /**
     * Add detailed breakdown sheets for large datasets
     */
    addDetailedBreakdownSheets(workbook, XLSX, smartReport, contentAnalysis) {
        const findings = smartReport.findings;

        // Add priority-specific sheets for large datasets
        if (contentAnalysis.sheetStrategy === 'multi-sheet-by-priority') {
            if (findings.highPriorityChanges?.length > 0) {
                this.addPrioritySpecificSheet(workbook, XLSX, findings.highPriorityChanges, 'HIGH Priority Details', contentAnalysis);
            }
            if (findings.moderatePriorityChanges?.length > 0) {
                this.addPrioritySpecificSheet(workbook, XLSX, findings.moderatePriorityChanges, 'MODERATE Priority Details', contentAnalysis);
            }
            if (findings.lowPriorityChanges?.length > 0) {
                this.addPrioritySpecificSheet(workbook, XLSX, findings.lowPriorityChanges, 'LOW Priority Details', contentAnalysis);
            }
        }
    }

    /**
     * Add priority-specific detailed sheet
     */
    addPrioritySpecificSheet(workbook, XLSX, changes, sheetName, contentAnalysis) {
        const detailData = [
            ['Employee ID', 'Employee Name', 'Section', 'Item', 'Change Type', 'Previous Value', 'Current Value', 'Difference', 'Description']
        ];

        changes.forEach(change => {
            detailData.push([
                change.employee_id || '',
                change.employee_name || '',
                change.section_name || '',
                change.item_label || change.item_name || '',
                change.change_type || change.category || '',
                this.formatValue(change.previous_value),
                this.formatValue(change.current_value),
                this.calculateDifference(change.previous_value, change.current_value),
                this.formatChangeFinding(change)
            ]);
        });

        const worksheet = XLSX.utils.aoa_to_sheet(detailData);

        // Apply dynamic column widths
        worksheet['!cols'] = [
            { width: contentAnalysis.columnWidths.employeeId },
            { width: contentAnalysis.columnWidths.employeeName },
            { width: contentAnalysis.columnWidths.section },
            { width: contentAnalysis.columnWidths.item },
            { width: 15 },
            { width: 15 },
            { width: 15 },
            { width: 15 },
            { width: contentAnalysis.columnWidths.description }
        ];

        XLSX.utils.book_append_sheet(workbook, worksheet, sheetName);
    }

    /**
     * Calculate difference between values
     */
    calculateDifference(prevValue, currValue) {
        try {
            const prev = parseFloat(prevValue) || 0;
            const curr = parseFloat(currValue) || 0;
            const diff = curr - prev;
            return diff.toFixed(2);
        } catch {
            return 'N/A';
        }
    }

    /**
     * Load SheetJS library dynamically
     */
    async loadSheetJS() {
        if (window.XLSX) {
            return window.XLSX;
        }
        
        // Load SheetJS from CDN if not available
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js';
            script.onload = () => {
                if (window.XLSX) {
                    resolve(window.XLSX);
                } else {
                    reject(new Error('SheetJS failed to load'));
                }
            };
            script.onerror = () => reject(new Error('Failed to load SheetJS library'));
            document.head.appendChild(script);
        });
    }

    /**
     * Add Executive Summary sheet
     */
    addSummarySheet(workbook, XLSX, smartReport) {
        const metadata = smartReport.metadata;
        const summary = smartReport.summary;
        const period = this.extractPeriodFromMetadata(metadata);
        
        const summaryData = [
            ['PAYROLL AUDIT REPORT: ' + period.month + ' ' + period.year],
            [],
            ['Report Information', 'Value'],
            ['Period', period.month + ' ' + period.year],
            ['Generated at', this.formatDateTime(metadata.generatedAt)],
            ['Generated By', metadata.generatedBy],
            ['Designation', metadata.designation],
            [],
            ['Executive Summary', 'Count'],
            ['Total Changes Detected', summary.totalChanges],
            ['HIGH Priority Changes', summary.highPriorityChanges],
            ['MODERATE Priority Changes', summary.moderatePriorityChanges],
            ['LOW Priority Changes', summary.lowPriorityChanges],
            ['Unique Employees Affected', summary.uniqueEmployees || 0],
            [],
            ['Report Sections', 'Items'],
            ...smartReport.sections.map(section => [section.title, section.count || 0])
        ];
        
        const worksheet = XLSX.utils.aoa_to_sheet(summaryData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 30 },
            { width: 20 }
        ];
        
        // Merge title cell
        worksheet['!merges'] = [{ s: { r: 0, c: 0 }, e: { r: 0, c: 1 } }];
        
        XLSX.utils.book_append_sheet(workbook, worksheet, this.templateStructure.workbook.sheets.summary);
    }

    /**
     * Add Detailed Findings sheet
     */
    addFindingsSheet(workbook, XLSX, smartReport) {
        const findings = smartReport.findings;
        
        // Prepare findings data
        const findingsData = [
            ['Employee ID', 'Employee Name', 'Section', 'Item', 'Change Type', 'Previous Value', 'Current Value', 'Priority', 'Description']
        ];
        
        // Add findings by priority
        const priorities = [
            { name: 'HIGH', changes: findings.highPriorityChanges || [] },
            { name: 'MODERATE', changes: findings.moderatePriorityChanges || [] },
            { name: 'LOW', changes: findings.lowPriorityChanges || [] }
        ];
        
        priorities.forEach(priority => {
            priority.changes.forEach(change => {
                findingsData.push([
                    change.employee_id || '',
                    change.employee_name || '',
                    change.section_name || '',
                    change.item_label || change.item_name || '',
                    change.change_type || change.category || '',
                    this.formatValue(change.previous_value),
                    this.formatValue(change.current_value),
                    priority.name,
                    this.formatChangeFinding(change)
                ]);
            });
        });
        
        const worksheet = XLSX.utils.aoa_to_sheet(findingsData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 12 }, // Employee ID
            { width: 25 }, // Employee Name
            { width: 20 }, // Section
            { width: 20 }, // Item
            { width: 15 }, // Change Type
            { width: 15 }, // Previous Value
            { width: 15 }, // Current Value
            { width: 10 }, // Priority
            { width: 50 }  // Description
        ];
        
        XLSX.utils.book_append_sheet(workbook, worksheet, this.templateStructure.workbook.sheets.findings);
    }

    /**
     * Add Employee Analysis sheet
     */
    addEmployeeAnalysisSheet(workbook, XLSX, smartReport) {
        // Group changes by employee
        const allChanges = [
            ...(smartReport.findings.highPriorityChanges || []),
            ...(smartReport.findings.moderatePriorityChanges || []),
            ...(smartReport.findings.lowPriorityChanges || [])
        ];
        
        const employeeGroups = this.groupChangesByEmployee(allChanges);
        
        const employeeData = [
            ['Employee ID', 'Employee Name', 'Section', 'Total Changes', 'HIGH Priority', 'MODERATE Priority', 'LOW Priority']
        ];
        
        Object.entries(employeeGroups).forEach(([employeeKey, empChanges]) => {
            const employee = empChanges[0];
            const priorityCounts = {
                HIGH: empChanges.filter(c => c.priority === 'HIGH').length,
                MODERATE: empChanges.filter(c => c.priority === 'MODERATE').length,
                LOW: empChanges.filter(c => c.priority === 'LOW').length
            };
            
            employeeData.push([
                employee.employee_id || '',
                employee.employee_name || '',
                employee.section_name || '',
                empChanges.length,
                priorityCounts.HIGH,
                priorityCounts.MODERATE,
                priorityCounts.LOW
            ]);
        });
        
        const worksheet = XLSX.utils.aoa_to_sheet(employeeData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 12 }, // Employee ID
            { width: 25 }, // Employee Name
            { width: 20 }, // Section
            { width: 15 }, // Total Changes
            { width: 12 }, // HIGH Priority
            { width: 15 }, // MODERATE Priority
            { width: 12 }  // LOW Priority
        ];
        
        XLSX.utils.book_append_sheet(workbook, worksheet, this.templateStructure.workbook.sheets.employees);
    }

    /**
     * Add Statistics sheet
     */
    addStatisticsSheet(workbook, XLSX, smartReport) {
        const summary = smartReport.summary;
        const findings = smartReport.findings;
        
        // Calculate additional statistics
        const allChanges = [
            ...(findings.highPriorityChanges || []),
            ...(findings.moderatePriorityChanges || []),
            ...(findings.lowPriorityChanges || [])
        ];
        
        const sectionStats = this.calculateSectionStatistics(allChanges);
        const changeTypeStats = this.calculateChangeTypeStatistics(allChanges);
        
        const statisticsData = [
            ['PAYROLL AUDIT STATISTICS'],
            [],
            ['Overall Summary', 'Count', 'Percentage'],
            ['Total Changes', summary.totalChanges, '100%'],
            ['HIGH Priority', summary.highPriorityChanges, this.calculatePercentage(summary.highPriorityChanges, summary.totalChanges)],
            ['MODERATE Priority', summary.moderatePriorityChanges, this.calculatePercentage(summary.moderatePriorityChanges, summary.totalChanges)],
            ['LOW Priority', summary.lowPriorityChanges, this.calculatePercentage(summary.lowPriorityChanges, summary.totalChanges)],
            [],
            ['Section Analysis', 'Changes', 'Percentage'],
            ...Object.entries(sectionStats).map(([section, count]) => [
                section, count, this.calculatePercentage(count, summary.totalChanges)
            ]),
            [],
            ['Change Type Analysis', 'Changes', 'Percentage'],
            ...Object.entries(changeTypeStats).map(([type, count]) => [
                type, count, this.calculatePercentage(count, summary.totalChanges)
            ])
        ];
        
        const worksheet = XLSX.utils.aoa_to_sheet(statisticsData);
        
        // Set column widths
        worksheet['!cols'] = [
            { width: 25 },
            { width: 15 },
            { width: 15 }
        ];
        
        XLSX.utils.book_append_sheet(workbook, worksheet, this.templateStructure.workbook.sheets.statistics);
    }

    // HELPER METHODS

    extractPeriodFromMetadata(metadata) {
        const date = new Date(metadata.generatedAt);
        return {
            month: date.toLocaleString('default', { month: 'long' }).toUpperCase(),
            year: date.getFullYear()
        };
    }

    formatDateTime(dateString) {
        const date = new Date(dateString);
        return date.toLocaleString('en-GB', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    groupChangesByEmployee(changes) {
        return changes.reduce((groups, change) => {
            const key = `${change.employee_id}-${change.employee_name}`;
            if (!groups[key]) groups[key] = [];
            groups[key].push(change);
            return groups;
        }, {});
    }

    formatValue(value) {
        if (value === null || value === undefined || value === '') return '0.00';
        const numValue = parseFloat(value);
        if (isNaN(numValue)) return value;
        return numValue.toFixed(2);
    }

    formatChangeFinding(change) {
        const itemName = change.item_name || change.item_label;
        const prevValue = this.formatValue(change.previous_value);
        const currValue = this.formatValue(change.current_value);
        const changeType = change.category || change.change_type;
        
        switch (changeType?.toUpperCase()) {
            case 'INCREASE':
                return `${itemName} increased from ${prevValue} to ${currValue}`;
            case 'DECREASE':
                return `${itemName} decreased from ${prevValue} to ${currValue}`;
            default:
                return `${itemName} changed from ${prevValue} to ${currValue}`;
        }
    }

    calculateSectionStatistics(changes) {
        return changes.reduce((stats, change) => {
            const section = change.section_name || 'Unknown';
            stats[section] = (stats[section] || 0) + 1;
            return stats;
        }, {});
    }

    calculateChangeTypeStatistics(changes) {
        return changes.reduce((stats, change) => {
            const type = change.change_type || change.category || 'Unknown';
            stats[type] = (stats[type] || 0) + 1;
            return stats;
        }, {});
    }

    calculatePercentage(value, total) {
        if (total === 0) return '0%';
        return ((value / total) * 100).toFixed(1) + '%';
    }
}

// Export for use in Final Report Interface
window.ExcelTemplateEngine = ExcelTemplateEngine;
