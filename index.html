<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>TEMPLAR PAYROLL AUDITOR - Professional Payroll Processing System</title>
  <link rel="stylesheet" href="styles.css">
  <link rel="stylesheet" href="ui/bank_adviser_styles.css">
  <link rel="stylesheet" href="ui/extraction_panel_blocker.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* Fast Splash screen styles */
    #splash-screen {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #1a237e 0%, #3f51b5 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
      transition: opacity 0.3s ease-out;
    }

    .splash-content {
      text-align: center;
      color: white;
      animation: fadeInUp 0.5s ease-out;
    }

    .splash-icon {
      font-size: 80px;
      margin-bottom: 20px;
      animation: fastPulse 1s infinite;
    }

    .splash-title {
      font-size: 32px;
      font-weight: bold;
      margin-bottom: 10px;
    }

    .splash-subtitle {
      font-size: 18px;
      margin-bottom: 30px;
      opacity: 0.9;
    }

    .loading-bar {
      width: 300px;
      height: 4px;
      background: rgba(255,255,255,0.3);
      border-radius: 2px;
      overflow: hidden;
      margin: 0 auto;
    }

    .loading-progress {
      height: 100%;
      background: #4caf50;
      width: 0%;
      animation: fastLoading 0.8s ease-out forwards;
    }

    @keyframes fastPulse {
      0%, 100% { transform: scale(1); opacity: 1; }
      50% { transform: scale(1.05); opacity: 0.8; }
    }

    @keyframes fastLoading {
      0% { width: 0%; }
      50% { width: 70%; }
      100% { width: 100%; }
    }

    @keyframes fadeInUp {
      0% {
        opacity: 0;
        transform: translateY(30px);
      }
      100% {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>
<body>
  <!-- Word Template Engine Test Button (Temporary) -->
  <div style="position: fixed; top: 20px; right: 20px; z-index: 99999; background: red; padding: 10px; border-radius: 5px;">
    <button id="test-word-engine" style="background: #ff0000; color: white; border: none; padding: 10px 15px; border-radius: 5px; font-weight: bold; cursor: pointer;">
      🧪 TEST WORD ENGINE
    </button>
  </div>

  <!-- Splash Screen -->
  <div id="splash-screen">
    <div class="splash-content">
      <div class="splash-icon">
        <i class="fas fa-file-invoice-dollar"></i>
      </div>
      <div class="splash-title">TEMPLAR PAYROLL AUDITOR</div>
      <div class="splash-subtitle">Professional Payroll Processing System</div>
      <div class="loading-bar">
        <div class="loading-progress"></div>
      </div>
    </div>
  </div>

  <!-- Main Application -->
  <div id="main-app" class="app-container" style="display: none;">
    <!-- Header -->
    <header class="app-header">
      <div class="header-content">
        <div class="logo-section">
          <i class="fas fa-file-invoice-dollar"></i>
          <div class="logo-text">
            <h1>TEMPLAR PAYROLL AUDITOR</h1>
            <div class="header-subtitle">
              <div class="header-created-by">Created By: SAMUEL ASIEDU</div>
              <div class="header-rights">© 2025 All Rights Reserved</div>
            </div>
          </div>
        </div>
        <div class="header-actions">
          <button id="settings-btn" class="header-btn" title="Settings">
            <i class="fas fa-cog"></i>
          </button>
          <button id="help-btn" class="header-btn" title="Help">
            <i class="fas fa-question-circle"></i>
          </button>
        </div>
      </div>
    </header>

    <!-- Navigation Tabs -->
    <nav class="main-nav">
      <button class="nav-tab" data-tab="home">
        <i class="fas fa-home"></i>
        Home
      </button>

      <button class="nav-tab active" data-tab="payroll-audit">
        <i class="fas fa-search"></i>
        Payroll Audit
      </button>
      <button class="nav-tab" data-tab="report-manager">
        <i class="fas fa-file-alt"></i>
        Report Manager
      </button>
      <button class="nav-tab" data-tab="pdf-sorter">
        <i class="fas fa-sort"></i>
        PDF Sorter
      </button>
      <button class="nav-tab" data-tab="data-builder">
        <i class="fas fa-database"></i>
        Data Builder
      </button>
      <button class="nav-tab" data-tab="dictionary">
        <i class="fas fa-book"></i>
        Dictionary Manager
      </button>
      <button class="nav-tab" data-tab="bank-adviser">
        <i class="fas fa-university"></i>
        Bank Adviser
      </button>
    </nav>

    <!-- Main Content Area -->
    <main class="main-content">
      <!-- Home Tab -->
      <div id="home-tab" class="tab-content">
        <div class="dashboard-grid">
          <!-- REMOVED: Redundant Payroll Comparison System - Use dedicated Payroll Audit tab instead -->

          <div class="dashboard-card">
            <div class="card-header">
              <h3><i class="fas fa-chart-line"></i> System Status</h3>
            </div>
            <div class="card-content">
              <div class="status-item">
                <span class="status-label">Backend Status:</span>
                <span id="backend-status" class="status-value">Ready</span>
              </div>
              <div class="status-item">
                <span class="status-label">Extraction Engine:</span>
                <span id="extraction-engine" class="status-value">Perfect + Hybrid</span>
              </div>
              <div class="status-item">
                <span class="status-label">Dictionary Items:</span>
                <span id="dictionary-count" class="status-value">Loading...</span>
              </div>
            </div>
          </div>

          <div class="dashboard-card">
            <div class="card-header">
              <h3><i class="fas fa-history"></i> Recent Activity</h3>
            </div>
            <div class="card-content">
              <div id="recent-activity" class="activity-list">
                <div class="activity-item">
                  <i class="fas fa-info-circle"></i>
                  <span>System initialized successfully</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Enhanced Payroll Audit Tab -->
      <div id="payroll-audit-tab" class="tab-content active">
        <div class="enhanced-payroll-audit-container">
          <!-- Enhanced Header with Phase Progress -->
          <div class="enhanced-audit-header">
            <h2><i class="fas fa-balance-scale"></i> Payroll Audit Process</h2>
          </div>

          <!-- Removed duplicate phase progress indicators - using only the real ones in enhanced progress panel -->

          <!-- Enhanced Real-Time Progress Panel (A2) -->
          <div id="enhanced-progress-panel" class="enhanced-progress-panel" style="display: none;">
            <!-- Phase Progress Indicators at Top - UPDATED SEQUENCE -->
            <div class="phase-progress-indicators-top">
              <div class="phase-indicator" data-phase="extraction" id="phase-extraction">
                <div class="phase-dot">
                  <i class="fas fa-file-upload"></i>
                </div>
                <span class="phase-name">EXTRACTION</span>
                <div class="phase-status">Pending</div>
              </div>
              <div class="phase-indicator" data-phase="comparison" id="phase-comparison">
                <div class="phase-dot">
                  <i class="fas fa-balance-scale"></i>
                </div>
                <span class="phase-name">COMPARISON</span>
                <div class="phase-status">Pending</div>
              </div>
              <div class="phase-indicator" data-phase="auto-learning" id="phase-auto-learning">
                <div class="phase-dot">
                  <i class="fas fa-brain"></i>
                </div>
                <span class="phase-name">AUTO LEARNING</span>
                <div class="phase-status">Pending</div>
              </div>
              <div class="phase-indicator" data-phase="tracker-feeding" id="phase-tracker-feeding">
                <div class="phase-dot">
                  <i class="fas fa-database"></i>
                </div>
                <span class="phase-name">TRACKER FEEDING</span>
                <div class="phase-status">Pending</div>
              </div>
              <div class="phase-indicator" data-phase="pre-reporting" id="phase-pre-reporting">
                <div class="phase-dot">
                  <i class="fas fa-filter"></i>
                </div>
                <span class="phase-name">PRE-REPORTING</span>
                <div class="phase-status">Pending</div>
              </div>
              <div class="phase-indicator" data-phase="report-generation" id="phase-report-generation">
                <div class="phase-dot">
                  <i class="fas fa-file-alt"></i>
                </div>
                <span class="phase-name">REPORT GENERATION</span>
                <div class="phase-status">Pending</div>
              </div>
            </div>

            <!-- Progress Header with Phase Indicator -->
            <div class="progress-header">
              <div class="current-phase-indicator">
                <div class="phase-icon" id="current-phase-icon">
                  <i class="fas fa-cogs"></i>
                </div>
                <div class="phase-info">
                  <h3 id="current-phase-title">Data Extraction Phase</h3>
                  <p id="current-phase-description">Extracting payroll data from PDF files...</p>
                </div>
              </div>
              <div class="progress-controls">
                <button id="pause-processing-btn" class="control-btn pause-btn">
                  <i class="fas fa-pause"></i> Pause
                </button>
                <button id="stop-processing-btn" class="control-btn stop-btn">
                  <i class="fas fa-stop"></i> Stop
                </button>
                <div id="processing-timer" class="processing-timer">
                  <i class="fas fa-clock"></i> <span id="timer-display">00:00</span>
                </div>
              </div>
            </div>

            <!-- Real-Time Progress Bar -->
            <div class="realtime-progress-container">
              <div class="progress-bar-enhanced">
                <div class="progress-fill-enhanced" id="progress-fill-enhanced"></div>
                <div class="progress-text-overlay">
                  <span id="progress-percentage-enhanced">0%</span>
                </div>
              </div>
              <div class="progress-status-text">
                <span id="progress-status-enhanced">Initializing...</span>
              </div>
            </div>

            <!-- Live Processing Statistics -->
            <div class="live-stats-grid">
              <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-users"></i></div>
                <div class="stat-content">
                  <div class="stat-value" id="employees-processed-enhanced">0</div>
                  <div class="stat-label">Employees Processed</div>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-exchange-alt"></i></div>
                <div class="stat-content">
                  <div class="stat-value" id="changes-detected-enhanced">0</div>
                  <div class="stat-label">Changes Detected</div>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-tachometer-alt"></i></div>
                <div class="stat-content">
                  <div class="stat-value" id="processing-speed">0</div>
                  <div class="stat-label">Records/Min</div>
                </div>
              </div>
              <div class="stat-card">
                <div class="stat-icon"><i class="fas fa-clock"></i></div>
                <div class="stat-content">
                  <div class="stat-value" id="estimated-time">--:--</div>
                  <div class="stat-label">Est. Remaining</div>
                </div>
              </div>
            </div>

            <!-- Real-Time Activity Feed -->
            <div class="activity-feed">
              <div class="activity-header">
                <h4><i class="fas fa-stream"></i> Live Activity</h4>
                <button id="clear-activity-btn" class="clear-btn">
                  <i class="fas fa-trash"></i> Clear
                </button>
              </div>
              <div class="activity-list" id="activity-list">
                <!-- Real-time activities will be populated here -->
              </div>
            </div>

            <!-- Phase indicators moved to top of enhanced progress panel -->
          </div>

          <!-- Content Switching Container -->
          <div id="content-switching-container" class="content-switching-container">

            <!-- Payroll Selection View (Initial State) -->
            <div id="payroll-selection-view" class="payroll-view">
              <!-- Phase 1: File Selection (Default) -->
              <div id="phase-1-content" class="phase-content active">
              <div class="file-selection-enhanced">
                <div class="file-grid">
                  <div class="file-section current-payroll">
                    <div class="section-header">
                      <h3><i class="fas fa-calendar-alt"></i> Current Month Payroll</h3>
                    </div>
                    <div class="file-upload-area" id="current-upload-area">
                      <i class="fas fa-file-pdf"></i>
                      <p>Select your current month payroll PDF</p>
                      <button id="browse-current-payroll" class="btn primary">Browse Files</button>
                    </div>
                    <div id="current-payroll-info" class="file-info"></div>
                    <div class="date-selection">
                      <div class="date-field">
                        <label>Month:</label>
                        <select id="current-month" class="form-select">
                          <option value="January">January</option>
                          <option value="February">February</option>
                          <option value="March">March</option>
                          <option value="April">April</option>
                          <option value="May">May</option>
                          <option value="June" selected>June</option>
                          <option value="July">July</option>
                          <option value="August">August</option>
                          <option value="September">September</option>
                          <option value="October">October</option>
                          <option value="November">November</option>
                          <option value="December">December</option>
                        </select>
                      </div>
                      <div class="date-field">
                        <label>Year:</label>
                        <select id="current-year" class="form-select">
                          <option value="2023">2023</option>
                          <option value="2024">2024</option>
                          <option value="2025" selected>2025</option>
                          <option value="2026">2026</option>
                        </select>
                      </div>
                    </div>
                  </div>

                  <div class="file-section previous-payroll">
                    <div class="section-header">
                      <h3><i class="fas fa-history"></i> Previous Month Payroll</h3>
                    </div>
                    <div class="file-upload-area" id="previous-upload-area">
                      <i class="fas fa-file-pdf"></i>
                      <p>Select your previous month payroll PDF</p>
                      <button id="browse-previous-payroll" class="btn primary">Browse Files</button>
                    </div>
                    <div id="previous-payroll-info" class="file-info"></div>
                    <div class="date-selection">
                      <div class="date-field">
                        <label>Month:</label>
                        <select id="previous-month" class="form-select">
                          <option value="January">January</option>
                          <option value="February">February</option>
                          <option value="March">March</option>
                          <option value="April">April</option>
                          <option value="May" selected>May</option>
                          <option value="June">June</option>
                          <option value="July">July</option>
                          <option value="August">August</option>
                          <option value="September">September</option>
                          <option value="October">October</option>
                          <option value="November">November</option>
                          <option value="December">December</option>
                        </select>
                      </div>
                      <div class="date-field">
                        <label>Year:</label>
                        <select id="previous-year" class="form-select">
                          <option value="2023">2023</option>
                          <option value="2024">2024</option>
                          <option value="2025" selected>2025</option>
                          <option value="2026">2026</option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Report Configuration moved to Final Report Interface -->

                <!-- Enhanced Action Buttons -->
                <div class="enhanced-actions">
                  <button id="start-payroll-audit" class="btn primary large enhanced-btn" disabled>
                    <i class="fas fa-rocket"></i> Start Enhanced Payroll Audit
                  </button>
                  <!-- Removed duplicate processing controls - enhanced progress panel handles all controls -->
                </div>

                <!-- Requirements Status -->
                <div class="requirements-status-enhanced">
                  <p id="audit-requirements">Please select both PDF files and complete the report signature to start the enhanced audit.</p>
                </div>
              </div>
            </div>
            
            <!-- Phase 5: Pre-Reporting (Interactive Selection) -->
            <div id="pre-reporting-content" class="phase-content">
              <div class="pre-reporting-header">
                <h2><i class="fas fa-filter"></i> Pre-Reporting: Interactive Change Selection</h2>
                <p>Review detected payroll changes and select which ones to include in the final report.</p>
              </div>
              
              <!-- Interactive Pre-reporting UI will be rendered here by interactive_pre_reporting.js -->
              <div id="pre-reporting-container">
                <!-- Loading spinner shown initially -->
                <div id="pre-reporting-loading-spinner" class="loading-container">
                  <div class="spinner"></div>
                  <p>Loading comparison data...</p>
                </div>
                
                <!-- Interactive UI container (initially hidden) -->
                <div id="pre-reporting-interactive-ui" style="display: none;">
                  <!-- InteractivePreReporting class will render content here -->
                </div>
              </div>
              
              <!-- Action buttons for pre-reporting phase -->
              <div class="pre-reporting-actions">
                <button id="back-to-comparison-btn" class="btn secondary">
                  <i class="fas fa-arrow-left"></i> Back to Comparison
                </button>
                <button id="generate-final-report-btn" class="btn primary large">
                  <i class="fas fa-file-contract"></i> Generate Final Report
                </button>
              </div>
            </div>
            
            <!-- Phase 5: Pre-Reporting (Interactive Selection) -->
            <div id="pre-reporting-panel" class="content-panel phase-content">
              <div class="panel-header">
                <h3><i class="fas fa-tasks"></i> Pre-Reporting: Change Review</h3>
                <span class="panel-status status-pending">Pending</span>
              </div>
              <div id="pre-reporting-content" class="panel-content">
                <!-- Interactive pre-reporting content will be populated by the module -->
                <div class="loading-placeholder">
                  <i class="fas fa-spinner fa-spin"></i>
                  <p>Loading change data for review...</p>
                </div>
              </div>
              <div class="panel-footer">
                <button id="back-to-comparison-btn" class="btn secondary">
                  <i class="fas fa-arrow-left"></i> Back to Comparison
                </button>
                <button id="continue-to-report-generation" class="btn primary large">
                  <i class="fas fa-file-contract"></i> Generate Final Report
                </button>
              </div>
            </div>
            
            <!-- Phase 6: Report Generation -->
            <div id="report-generation-panel" class="content-panel phase-content">
              <div class="panel-header">
                <h3><i class="fas fa-file-alt"></i> Report Generation</h3>
                <span class="panel-status status-pending">Pending</span>
              </div>
              <div id="report-generation-content" class="panel-content">
                <!-- Report generation content will be populated by the module -->
                <div class="loading-placeholder">
                  <i class="fas fa-spinner fa-spin"></i>
                  <p>Preparing report generation...</p>
                </div>
              </div>
            </div>
            </div> <!-- End payroll-selection-view -->

            <!-- Payroll Processing View (Hidden Initially) -->
            <div id="payroll-processing-view" class="payroll-view hidden">
              <!-- Processing phases content will be shown here -->
              <div class="processing-content">
                <div class="processing-header">
                  <h3><i class="fas fa-cogs"></i> Processing Payroll Audit</h3>
                  <p>Please wait while we process your payroll data...</p>
                </div>

                <!-- Enhanced progress panel will be shown here during processing -->
                <div id="processing-progress-container">
                  <!-- Progress content populated by phase manager -->
                </div>

                <!-- Pre-reporting interface will be loaded here when ready -->
                <div id="processing-pre-reporting-container">
                  <!-- Pre-reporting UI populated by interactive_pre_reporting.js -->
                </div>
              </div>
            </div> <!-- End payroll-processing-view -->

          </div>
        </div>
      </div>

      <!-- Dictionary Manager Tab -->
      <div id="dictionary-tab" class="tab-content">
        <div id="dictionary-manager-container">
          <div class="dictionary-header">
            <h2>Dictionary Manager</h2>
            <p>Manage payroll item dictionaries and mappings for accurate extraction.</p>

            <div class="dictionary-actions">
              <button id="import-dictionary-btn" class="btn secondary">
                <i class="fas fa-upload"></i> Import Dictionary
              </button>
              <button id="export-dictionary-btn" class="btn secondary">
                <i class="fas fa-download"></i> Export Dictionary
              </button>
              <button id="reset-dictionary-btn" class="btn secondary">
                <i class="fas fa-undo"></i> Reset to Defaults
              </button>
              <button id="save-dictionary-btn" class="btn primary">
                <i class="fas fa-save"></i> Save Dictionary
              </button>
            </div>
          </div>

          <div class="dictionary-tabs">
            <button class="dict-tab active" data-section="personal-details">
              <i class="fas fa-user"></i> Personal Details
            </button>
            <button class="dict-tab" data-section="earnings">
              <i class="fas fa-money-bill-wave"></i> Earnings
            </button>
            <button class="dict-tab" data-section="deductions">
              <i class="fas fa-minus-circle"></i> Deductions
            </button>
            <button class="dict-tab" data-section="employers-contribution">
              <i class="fas fa-building"></i> Employer Contributions
            </button>
            <button class="dict-tab" data-section="loans">
              <i class="fas fa-credit-card"></i> Loans
            </button>
            <button class="dict-tab" data-section="bank-details">
              <i class="fas fa-university"></i> Bank Details
            </button>
            <button class="dict-tab" data-section="auto-learning">
              <i class="fas fa-brain"></i> Auto-Learning
            </button>
          </div>



          <div class="dictionary-content">
            <!-- Personal Details Section -->
            <div id="personal-details-content" class="dict-section-content active">
              <div class="section-header">
                <h3>Personal Details Items</h3>
                <button id="add-personal-details-item-btn" class="btn primary">
                  <i class="fas fa-plus"></i> Add Item
                </button>
              </div>
              <div class="items-table-container">
                <table class="items-table">
                  <thead>
                    <tr>
                      <th>Item Name</th>
                      <th class="change-detection-header">NEW</th>
                      <th class="change-detection-header">INCREASE</th>
                      <th class="change-detection-header">DECREASE</th>
                      <th class="change-detection-header">REMOVED</th>
                      <th class="change-detection-header">NO_CHANGE</th>
                      <th>Format</th>
                      <th>Value Format</th>
                      <th>Include in Report</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="personal-details-items">
                    <!-- Items will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Earnings Section -->
            <div id="earnings-content" class="dict-section-content">
              <div class="section-header">
                <h3>Earnings Items</h3>
                <button id="add-earnings-item-btn" class="btn primary">
                  <i class="fas fa-plus"></i> Add Item
                </button>
              </div>
              <div class="items-table-container">
                <table class="items-table">
                  <thead>
                    <tr>
                      <th>Item Name</th>
                      <th class="change-detection-header">NEW</th>
                      <th class="change-detection-header">INCREASE</th>
                      <th class="change-detection-header">DECREASE</th>
                      <th class="change-detection-header">REMOVED</th>
                      <th class="change-detection-header">NO_CHANGE</th>
                      <th>Format</th>
                      <th>Value Format</th>
                      <th>Include in Report</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="earnings-items">
                    <!-- Items will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Deductions Section -->
            <div id="deductions-content" class="dict-section-content">
              <div class="section-header">
                <h3>Deductions Items</h3>
                <button id="add-deductions-item-btn" class="btn primary">
                  <i class="fas fa-plus"></i> Add Item
                </button>
              </div>
              <div class="items-table-container">
                <table class="items-table">
                  <thead>
                    <tr>
                      <th>Item Name</th>
                      <th class="change-detection-header">NEW</th>
                      <th class="change-detection-header">INCREASE</th>
                      <th class="change-detection-header">DECREASE</th>
                      <th class="change-detection-header">REMOVED</th>
                      <th class="change-detection-header">NO_CHANGE</th>
                      <th>Format</th>
                      <th>Value Format</th>
                      <th>Include in Report</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="deductions-items">
                    <!-- Items will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Employer Contributions Section -->
            <div id="employers-contribution-content" class="dict-section-content">
              <div class="section-header">
                <h3>Employer Contributions Items</h3>
                <button id="add-employers-contribution-item-btn" class="btn primary">
                  <i class="fas fa-plus"></i> Add Item
                </button>
              </div>
              <div class="items-table-container">
                <table class="items-table">
                  <thead>
                    <tr>
                      <th>Item Name</th>
                      <th class="change-detection-header">NEW</th>
                      <th class="change-detection-header">INCREASE</th>
                      <th class="change-detection-header">DECREASE</th>
                      <th class="change-detection-header">REMOVED</th>
                      <th class="change-detection-header">NO_CHANGE</th>
                      <th>Format</th>
                      <th>Value Format</th>
                      <th>Include in Report</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="employers-contribution-items">
                    <!-- Items will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Enhanced Loans Section with Hierarchical Structure -->
            <div id="loans-content" class="dict-section-content">
              <div class="section-header">
                <h3>Loans Management</h3>
                <div class="loans-actions">
                  <button id="create-loan-type-btn" class="btn primary">
                    <i class="fas fa-plus"></i> Create Loan Type
                  </button>
                  <button id="auto-group-loans-btn" class="btn secondary">
                    <i class="fas fa-layer-group"></i> Auto-Group Items
                  </button>
                  <button id="classification-report-btn" class="btn info">
                    <i class="fas fa-chart-pie"></i> Classification Report
                  </button>
                  <button id="force-traditional-table-btn" class="btn warning">
                    <i class="fas fa-table"></i> Show Traditional Table
                  </button>
                  <button id="refresh-traditional-table-btn" class="btn warning">
                    <i class="fas fa-sync"></i> Refresh Traditional Table
                  </button>
                </div>
              </div>

              <!-- Classification Summary Panel -->
              <div class="classification-summary">
                <div class="summary-card in-house">
                  <div class="summary-icon"><i class="fas fa-building"></i></div>
                  <div class="summary-content">
                    <div class="summary-count" id="in-house-count">0</div>
                    <div class="summary-label">IN-HOUSE LOANS</div>
                  </div>
                </div>
                <div class="summary-card external">
                  <div class="summary-icon"><i class="fas fa-university"></i></div>
                  <div class="summary-content">
                    <div class="summary-count" id="external-count">0</div>
                    <div class="summary-label">EXTERNAL LOANS</div>
                  </div>
                </div>
                <div class="summary-card unclassified">
                  <div class="summary-icon"><i class="fas fa-question-circle"></i></div>
                  <div class="summary-content">
                    <div class="summary-count" id="unclassified-count">0</div>
                    <div class="summary-label">UNCLASSIFIED</div>
                  </div>
                </div>
              </div>

              <!-- Hierarchical Loan Types Display -->
              <div class="loan-types-container">
                <div class="loan-types-header">
                  <h4>Loan Types & Items</h4>
                  <div class="view-controls">
                    <button id="expand-all-loans" class="btn-icon" title="Expand All">
                      <i class="fas fa-expand-alt"></i>
                    </button>
                    <button id="collapse-all-loans" class="btn-icon" title="Collapse All">
                      <i class="fas fa-compress-alt"></i>
                    </button>
                  </div>
                </div>

                <div id="loan-types-tree" class="loan-types-tree">
                  <!-- Hierarchical loan types will be populated here -->
                </div>
              </div>

              <!-- Column Headers Section -->
              <div class="column-headers-section">
                <h4>Column Headers (Fixed)</h4>
                <div class="column-headers-grid" id="column-headers-grid">
                  <!-- Column headers will be populated here -->
                </div>
              </div>

              <!-- Traditional Item Management Section -->
              <div class="traditional-loans-section">
                <div class="section-divider">
                  <h4><i class="fas fa-table"></i> Item Management (Format, Value Format, Include in Report)</h4>
                  <p class="section-description">Manage individual loan items with their formats and reporting settings</p>
                  <button id="add-loans-item-btn" class="btn primary" style="margin-left: 15px;">
                    <i class="fas fa-plus"></i> Add Loan Item
                  </button>
                </div>
                <div class="table-container">
                  <table class="dictionary-table">
                    <thead>
                      <tr>
                        <th>Item Name</th>
                        <th class="change-detection-header">NEW</th>
                        <th class="change-detection-header">INCREASE</th>
                        <th class="change-detection-header">DECREASE</th>
                        <th class="change-detection-header">REMOVED</th>
                        <th class="change-detection-header">NO_CHANGE</th>
                        <th>Format</th>
                        <th>Value Format</th>
                        <th>Include in Report</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody id="loans-items">
                      <!-- Traditional loan items will be populated here -->
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- Bank Details Section -->
            <div id="bank-details-content" class="dict-section-content">
              <div class="section-header">
                <h3>Bank Details Items</h3>
                <button id="add-bank-details-item-btn" class="btn primary">
                  <i class="fas fa-plus"></i> Add Item
                </button>
              </div>
              <div class="items-table-container">
                <table class="items-table">
                  <thead>
                    <tr>
                      <th>Item Name</th>
                      <th class="change-detection-header">NEW</th>
                      <th class="change-detection-header">INCREASE</th>
                      <th class="change-detection-header">DECREASE</th>
                      <th class="change-detection-header">REMOVED</th>
                      <th class="change-detection-header">NO_CHANGE</th>
                      <th>Format</th>
                      <th>Value Format</th>
                      <th>Include in Report</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody id="bank-details-items">
                    <!-- Items will be populated here -->
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Auto-Learning Section -->
            <div id="auto-learning-content" class="dict-section-content">
              <div class="auto-learning-header">
                <h3>Auto-Learning System</h3>
                <div class="auto-learning-controls">
                  <button id="refresh-pending-items" class="btn secondary">
                    <i class="fas fa-sync"></i> Refresh
                  </button>
                  <button id="approve-all-pending" class="btn success">
                    <i class="fas fa-check-double"></i> Approve All
                  </button>
                  <button id="reject-all-pending" class="btn danger">
                    <i class="fas fa-times-circle"></i> Reject All
                  </button>
                </div>
              </div>

              <div class="auto-learning-stats">
                <div class="stat-card">
                  <div class="stat-value" id="pending-items-count">0</div>
                  <div class="stat-label">Pending Items</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value" id="auto-added-count">0</div>
                  <div class="stat-label">Auto-Added Today</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value" id="learning-accuracy">0%</div>
                  <div class="stat-label">Learning Accuracy</div>
                </div>
                <div class="stat-card">
                  <div class="stat-value" id="learning-status">Ready</div>
                  <div class="stat-label">Status</div>
                </div>
              </div>

              <div class="pending-items-container">
                <h4>Pending Items for Approval</h4>
                <div class="pending-items-table-container">
                  <table class="pending-items-table">
                    <thead>
                      <tr>
                        <th>Item Name</th>
                        <th>Suggested Section</th>
                        <th>Value</th>
                        <th>Confidence</th>
                        <th>Source</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody id="pending-items-list">
                      <!-- Pending items will be populated here -->
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="activity-log-container">
                <h4>Recent Activity</h4>
                <div class="activity-log" id="auto-learning-activity-log">
                  <!-- Activity log will be populated here -->
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Item Edit Modal -->
        <div id="item-edit-modal" class="modal" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <h3 id="edit-modal-title">Edit Item</h3>
              <span class="close-modal">&times;</span>
            </div>
            <div class="modal-body">
              <div class="form-group">
                <label for="edit-item-name">Item Name:</label>
                <input type="text" id="edit-item-name" class="form-input" placeholder="Enter item name">
              </div>
              <div class="form-group">
                <label for="edit-item-format">Format:</label>
                <input type="text" id="edit-item-format" class="form-input" placeholder="Enter format pattern">
              </div>
              <div class="form-group">
                <label for="edit-item-value-format">Value Format:</label>
                <select id="edit-item-value-format" class="form-input">
                  <option value="">Select format</option>
                  <option value="text">Text</option>
                  <option value="number">Number</option>
                  <option value="currency">Currency</option>
                  <option value="date">Date</option>
                </select>
              </div>
              <div class="form-group">
                <label for="edit-item-standardized-name">Standardized Name:</label>
                <input type="text" id="edit-item-standardized-name" class="form-input" placeholder="Enter standardized name">
              </div>
              <div class="form-group">
                <label for="edit-item-variations">Variations (comma-separated):</label>
                <input type="text" id="edit-item-variations" class="form-input" placeholder="Enter variations">
              </div>
              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" id="edit-item-include-in-report" checked>
                  Include in Report
                </label>
              </div>
              <!-- Loan Classification Field (only shown for LOANS section) -->
              <div class="form-group" id="loan-classification-group" style="display: none;">
                <label for="edit-loan-classification">Loan Classification:</label>
                <select id="edit-loan-classification" class="form-input">
                  <option value="IN-HOUSE LOAN">IN-HOUSE LOAN</option>
                  <option value="EXTERNAL LOAN">EXTERNAL LOAN</option>
                </select>
              </div>
            </div>
            <div class="modal-footer">
              <button id="save-item-btn" class="btn primary">Save</button>
              <button id="cancel-edit-btn" class="btn secondary">Cancel</button>
            </div>
          </div>
        </div>

        <!-- Section Selection Modal for Auto-Learning -->
        <div id="section-selection-modal" class="modal" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <h3>Select Section for Item</h3>
              <span class="close-modal" id="close-section-modal">&times;</span>
            </div>
            <div class="modal-body">
              <div class="form-group">
                <label for="item-name-display">Item Name:</label>
                <input type="text" id="item-name-display" class="form-input" readonly>
              </div>
              <div class="form-group">
                <label for="item-value-display">Value:</label>
                <input type="text" id="item-value-display" class="form-input" readonly>
              </div>
              <div class="form-group">
                <label for="section-select">Target Section:</label>
                <select id="section-select" class="form-input">
                  <option value="PERSONAL DETAILS">Personal Details</option>
                  <option value="EARNINGS">Earnings</option>
                  <option value="DEDUCTIONS">Deductions</option>
                  <option value="EMPLOYERS CONTRIBUTION">Employer Contributions</option>
                  <option value="LOANS">Loans</option>
                  <option value="EMPLOYEE BANK DETAILS">Bank Details</option>
                </select>
              </div>
            </div>
            <div class="modal-footer">
              <button id="approve-with-section-btn" class="btn success">Approve</button>
              <button id="cancel-section-selection" class="btn secondary">Cancel</button>
            </div>
          </div>
        </div>

        <!-- Create Loan Type Modal -->
        <div id="create-loan-type-modal" class="modal" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <h3>Create New Loan Type</h3>
              <span class="close-modal" id="close-loan-type-modal">&times;</span>
            </div>
            <div class="modal-body">
              <div class="form-group">
                <label for="loan-type-name">Loan Type Name:</label>
                <input type="text" id="loan-type-name" class="form-input" placeholder="e.g., RENT ADVANCE, STAFF LOAN">
              </div>
              <div class="form-group">
                <label for="loan-classification">Classification:</label>
                <select id="loan-classification" class="form-input">
                  <option value="IN-HOUSE LOAN">IN-HOUSE LOAN</option>
                  <option value="EXTERNAL LOAN">EXTERNAL LOAN</option>
                </select>
              </div>
              <div class="form-group">
                <label>Auto-detect related items:</label>
                <div id="auto-detected-items" class="auto-detected-items">
                  <!-- Auto-detected items will be populated here -->
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button id="create-loan-type-save" class="btn primary">Create Loan Type</button>
              <button id="cancel-loan-type-creation" class="btn secondary">Cancel</button>
            </div>
          </div>
        </div>

        <!-- Edit Loan Type Modal -->
        <div id="edit-loan-type-modal" class="modal" style="display: none;">
          <div class="modal-content">
            <div class="modal-header">
              <h3>Edit Loan Type</h3>
              <span class="close-modal" id="close-edit-loan-type-modal">&times;</span>
            </div>
            <div class="modal-body">
              <div class="form-group">
                <label for="edit-loan-type-name">Loan Type Name:</label>
                <input type="text" id="edit-loan-type-name" class="form-input" readonly>
              </div>
              <div class="form-group">
                <label for="edit-loan-classification">Classification:</label>
                <select id="edit-loan-classification" class="form-input">
                  <option value="IN-HOUSE LOAN">IN-HOUSE LOAN</option>
                  <option value="EXTERNAL LOAN">EXTERNAL LOAN</option>
                </select>
              </div>
              <div class="form-group">
                <label>Associated Items:</label>
                <div id="loan-type-items-list" class="loan-type-items-list">
                  <!-- Loan type items will be populated here -->
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button id="save-loan-type-changes" class="btn primary">Save Changes</button>
              <button id="cancel-loan-type-edit" class="btn secondary">Cancel</button>
            </div>
          </div>
        </div>

        <!-- Classification Report Modal -->
        <div id="classification-report-modal" class="modal" style="display: none;">
          <div class="modal-content large-modal">
            <div class="modal-header">
              <h3>Loan Classification Report</h3>
              <span class="close-modal" id="close-classification-report">&times;</span>
            </div>
            <div class="modal-body">
              <div class="classification-report-content">
                <div class="report-summary">
                  <div class="report-card">
                    <h4>IN-HOUSE LOANS</h4>
                    <div id="in-house-report-content" class="report-content">
                      <!-- IN-HOUSE loans will be populated here -->
                    </div>
                  </div>
                  <div class="report-card">
                    <h4>EXTERNAL LOANS</h4>
                    <div id="external-report-content" class="report-content">
                      <!-- EXTERNAL loans will be populated here -->
                    </div>
                  </div>
                  <div class="report-card">
                    <h4>UNCLASSIFIED</h4>
                    <div id="unclassified-report-content" class="report-content">
                      <!-- UNCLASSIFIED loans will be populated here -->
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="modal-footer">
              <button id="export-classification-report" class="btn primary">
                <i class="fas fa-download"></i> Export Report
              </button>
              <button id="close-classification-report-btn" class="btn secondary">Close</button>
            </div>
          </div>
        </div>
      </div>



      <!-- Report Manager Tab -->
      <div id="report-manager-tab" class="tab-content">
        <div class="report-manager-container">
          <!-- Header Section -->
          <div class="report-manager-header">
            <div class="header-content">
              <h2><i class="fas fa-file-alt"></i> Report Manager</h2>
              <p>Access, manage, and download all your payroll audit reports</p>
            </div>
            <div class="header-actions">
              <button id="refresh-reports" class="btn secondary">
                <i class="fas fa-sync"></i> Refresh
              </button>
              <button id="export-all-reports" class="btn primary">
                <i class="fas fa-download"></i> Export All
              </button>
            </div>
          </div>

          <!-- Statistics Overview -->
          <div class="reports-stats">
            <div class="stat-card">
              <div class="stat-icon"><i class="fas fa-file-alt"></i></div>
              <div class="stat-content">
                <div class="stat-value" id="total-reports-count">0</div>
                <div class="stat-label">Total Reports</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon"><i class="fas fa-calendar-week"></i></div>
              <div class="stat-content">
                <div class="stat-value" id="this-month-reports">0</div>
                <div class="stat-label">This Month</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon"><i class="fas fa-chart-line"></i></div>
              <div class="stat-content">
                <div class="stat-value" id="total-employees-audited">0</div>
                <div class="stat-label">Employees Audited</div>
              </div>
            </div>
            <div class="stat-card">
              <div class="stat-icon"><i class="fas fa-exclamation-triangle"></i></div>
              <div class="stat-content">
                <div class="stat-value" id="total-changes-detected">0</div>
                <div class="stat-label">Changes Detected</div>
              </div>
            </div>
          </div>

          <!-- Tab-Based Navigation -->
          <div class="section-navigation">
            <h3><i class="fas fa-folder"></i> Report Folders</h3>
            <div class="section-tabs">
              <button class="section-tab active" data-section="all">
                <i class="fas fa-list"></i> All Reports
              </button>
              <button class="section-tab" data-section="Payroll_Audit_Reports">
                <i class="fas fa-balance-scale"></i> Payroll Audit Reports
              </button>
              <button class="section-tab" data-section="PDF_Sorter_Reports">
                <i class="fas fa-sort"></i> PDF Sorter Reports
              </button>
              <button class="section-tab" data-section="Data_Builder_Reports">
                <i class="fas fa-database"></i> Data Builder Reports
              </button>
              <button class="section-tab" data-section="Bank_Adviser_Reports">
                <i class="fas fa-university"></i> Bank Adviser Reports
              </button>
            </div>
          </div>

          <!-- Filters and Search -->
          <div class="reports-filters">
            <div class="filter-group">
              <label for="report-search">Search Reports:</label>
              <input type="text" id="report-search" class="form-input" placeholder="Search by date, name, or designation...">
            </div>
            <div class="filter-group">
              <label for="date-filter">Date Range:</label>
              <select id="date-filter" class="form-input">
                <option value="all">All Time</option>
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
            </div>
            <div class="filter-group">
              <label for="report-type-filter">Report Type:</label>
              <select id="report-type-filter" class="form-input">
                <option value="all">All Types</option>
                <option value="traditional">Traditional Reports</option>
                <option value="label-based">Label-Based Reports</option>
                <option value="comparison">Comparison Reports</option>
              </select>
            </div>
          </div>

          <!-- Reports Table -->
          <div class="reports-table-container">
            <table class="reports-table">
              <thead>
                <tr>
                  <th>Date & Time</th>
                  <th>Report Period</th>
                  <th>Generated By</th>
                  <th>Source Tab</th>
                  <th>Employees</th>
                  <th>Changes</th>
                  <th>Report Type</th>
                  <th>Status</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody id="reports-table-body">
                <!-- Reports will be populated here -->
              </tbody>
            </table>
          </div>

          <!-- Empty State -->
          <div id="reports-empty-state" class="empty-state" style="display: none;">
            <div class="empty-icon">
              <i class="fas fa-file-alt"></i>
            </div>
            <h3>No Reports Found</h3>
            <p>You haven't generated any payroll audit reports yet.</p>
            <p>Complete a payroll audit to see your reports here.</p>
            <button class="btn primary" onclick="switchToTab('payroll-audit')">
              <i class="fas fa-plus"></i> Create First Report
            </button>
          </div>

          <!-- Loading State -->
          <div id="reports-loading" class="loading-state" style="display: none;">
            <div class="loading-spinner">
              <i class="fas fa-spinner fa-spin"></i>
            </div>
            <p>Loading reports...</p>
          </div>
        </div>
      </div>

      <!-- PDF Sorter Tab -->
      <div id="pdf-sorter-tab" class="tab-content">
        <div class="pdf-sorter-container">
          <div class="pdf-sorter-header">
            <h2><i class="fas fa-sort"></i> PDF Sorter</h2>
            <p class="tab-description">Sort and rearrange PDF payslips by Employee ID, Name, Section, Department, or Job Title</p>
          </div>

          <!-- PDF Upload Section -->
          <div class="pdf-sorter-upload-section">
            <div class="upload-card">
              <div class="upload-header">
                <h3><i class="fas fa-upload"></i> Upload PDF File</h3>
                <p>Select a PDF file containing multiple payslips to sort</p>
              </div>

              <div class="upload-area" id="pdf-upload-area">
                <div class="upload-content">
                  <i class="fas fa-file-pdf upload-icon"></i>
                  <p class="upload-text">Click the button below to select your PDF file</p>
                  <input type="file" id="pdf-file-input" accept=".pdf" style="display: none;">
                  <button class="btn btn-primary" id="select-pdf-btn">
                    <i class="fas fa-folder-open"></i> Select PDF File
                  </button>
                </div>
              </div>

              <div class="file-info" id="pdf-file-info" style="display: none;">
                <div class="file-details">
                  <div class="file-name">
                    <i class="fas fa-file-pdf"></i>
                    <span id="selected-file-name">No file selected</span>
                  </div>
                  <div class="file-stats">
                    <span class="file-size" id="file-size">0 MB</span>
                    <span class="file-pages" id="estimated-pages">0 pages</span>
                  </div>
                </div>
                <button class="btn btn-secondary btn-sm" id="change-file-btn">
                  <i class="fas fa-edit"></i> Change File
                </button>
              </div>
            </div>
          </div>

          <!-- Sorting Configuration Section -->
          <div class="pdf-sorter-config-section" id="sorting-config" style="display: none;">
            <div class="config-card">
              <div class="config-header">
                <h3><i class="fas fa-cogs"></i> Sorting Configuration</h3>
                <p>Configure how you want to sort the payslips</p>
              </div>

              <!-- Primary Sorting -->
              <div class="sorting-level">
                <div class="level-header">
                  <h4><i class="fas fa-sort-amount-down"></i> Primary Sorting</h4>
                  <span class="level-badge">Required</span>
                </div>
                <div class="sorting-options">
                  <div class="sort-option">
                    <input type="radio" id="sort-employee-no" name="primary-sort" value="employee_no" checked>
                    <label for="sort-employee-no">
                      <i class="fas fa-id-badge"></i>
                      <span class="option-title">Employee No.</span>
                      <span class="option-desc">Sort by employee ID (COP####, PW####, SEC####, E####, PGH####)</span>
                    </label>
                  </div>
                  <div class="sort-option">
                    <input type="radio" id="sort-employee-name" name="primary-sort" value="employee_name">
                    <label for="sort-employee-name">
                      <i class="fas fa-user"></i>
                      <span class="option-title">Employee Name</span>
                      <span class="option-desc">Sort alphabetically by first name (A-Z)</span>
                    </label>
                  </div>
                  <div class="sort-option">
                    <input type="radio" id="sort-section" name="primary-sort" value="section">
                    <label for="sort-section">
                      <i class="fas fa-sitemap"></i>
                      <span class="option-title">Section</span>
                      <span class="option-desc">Sort alphabetically by section</span>
                    </label>
                  </div>
                  <div class="sort-option">
                    <input type="radio" id="sort-department" name="primary-sort" value="department">
                    <label for="sort-department">
                      <i class="fas fa-building"></i>
                      <span class="option-title">Department</span>
                      <span class="option-desc">Sort alphabetically by department</span>
                    </label>
                  </div>
                  <div class="sort-option">
                    <input type="radio" id="sort-job-title" name="primary-sort" value="job_title">
                    <label for="sort-job-title">
                      <i class="fas fa-briefcase"></i>
                      <span class="option-title">Job Title</span>
                      <span class="option-desc">Sort alphabetically by job title</span>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Secondary Sorting -->
              <div class="sorting-level">
                <div class="level-header">
                  <h4><i class="fas fa-sort-amount-down-alt"></i> Secondary Sorting</h4>
                  <span class="level-badge optional">Optional</span>
                  <label class="toggle-switch">
                    <input type="checkbox" id="enable-secondary-sort">
                    <span class="slider"></span>
                  </label>
                </div>
                <div class="sorting-options" id="secondary-sort-options" style="display: none;">
                  <select id="secondary-sort-select" class="form-select">
                    <option value="">Select secondary sorting criteria</option>
                    <option value="employee_no">Employee No.</option>
                    <option value="employee_name">Employee Name</option>
                    <option value="section">Section</option>
                    <option value="department">Department</option>
                    <option value="job_title">Job Title</option>
                  </select>
                </div>
              </div>

              <!-- Tertiary Sorting -->
              <div class="sorting-level">
                <div class="level-header">
                  <h4><i class="fas fa-sort-numeric-down"></i> Tertiary Sorting</h4>
                  <span class="level-badge optional">Optional</span>
                  <label class="toggle-switch">
                    <input type="checkbox" id="enable-tertiary-sort">
                    <span class="slider"></span>
                  </label>
                </div>
                <div class="sorting-options" id="tertiary-sort-options" style="display: none;">
                  <select id="tertiary-sort-select" class="form-select">
                    <option value="">Select tertiary sorting criteria</option>
                    <option value="employee_no">Employee No.</option>
                    <option value="employee_name">Employee Name</option>
                    <option value="section">Section</option>
                    <option value="department">Department</option>
                    <option value="job_title">Job Title</option>
                  </select>
                </div>
              </div>

              <!-- Sort Order -->
              <div class="sorting-level">
                <div class="level-header">
                  <h4><i class="fas fa-arrows-alt-v"></i> Sort Order</h4>
                </div>
                <div class="sorting-options">
                  <div class="sort-order-options">
                    <label class="radio-option">
                      <input type="radio" name="sort-order" value="ascending" checked>
                      <span class="radio-custom"></span>
                      <i class="fas fa-sort-alpha-down"></i>
                      Ascending (A-Z, 1-9)
                    </label>
                    <label class="radio-option">
                      <input type="radio" name="sort-order" value="descending">
                      <span class="radio-custom"></span>
                      <i class="fas fa-sort-alpha-up"></i>
                      Descending (Z-A, 9-1)
                    </label>
                  </div>
                </div>
              </div>

              <!-- Report Information -->
              <div class="sorting-level">
                <div class="level-header">
                  <h4><i class="fas fa-user-edit"></i> Report Information</h4>
                  <span class="level-badge optional">Optional</span>
                </div>
                <div class="sorting-options">
                  <div class="report-info-fields">
                    <div class="field-group">
                      <label for="sorter-report-name">Report Generated By:</label>
                      <input type="text" id="sorter-report-name" class="form-input" placeholder="Enter your name" value="SAMUEL ASIEDU">
                    </div>
                    <div class="field-group">
                      <label for="sorter-report-designation">Designation:</label>
                      <input type="text" id="sorter-report-designation" class="form-input" placeholder="Enter your designation" value="PAYROLL AUDITOR">
                    </div>
                  </div>
                </div>
              </div>

              <!-- Action Buttons -->
              <div class="config-actions">
                <button class="btn btn-success btn-lg" id="start-sorting-btn">
                  <i class="fas fa-play"></i> Start Sorting
                </button>
                <button class="btn btn-secondary" id="reset-config-btn">
                  <i class="fas fa-undo"></i> Reset Configuration
                </button>
              </div>
            </div>
          </div>

          <!-- Processing Section -->
          <div class="pdf-sorter-processing-section" id="sorting-progress" style="display: none;">
            <div class="processing-card">
              <div class="processing-header">
                <h3><i class="fas fa-cog fa-spin"></i> Processing PDF</h3>
                <p>Extracting and sorting payslips...</p>
              </div>

              <div class="progress-container">
                <div class="progress-bar">
                  <div class="progress-fill" id="sort-progress-fill"></div>
                </div>
                <div class="progress-text">
                  <span id="sort-progress-percentage">0%</span>
                  <span id="sort-progress-status">Initializing...</span>
                </div>
              </div>

              <div class="processing-stats">
                <div class="stat-item">
                  <span class="stat-label">Pages Processed:</span>
                  <span class="stat-value" id="pages-processed">0</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Payslips Found:</span>
                  <span class="stat-value" id="payslips-found">0</span>
                </div>
                <div class="stat-item">
                  <span class="stat-label">Current Stage:</span>
                  <span class="stat-value" id="current-stage">Extraction</span>
                </div>
              </div>

              <div class="processing-actions">
                <button class="btn btn-danger" id="cancel-sorting-btn">
                  <i class="fas fa-stop"></i> Cancel Processing
                </button>
              </div>
            </div>
          </div>

          <!-- Results Section -->
          <div class="pdf-sorter-results-section" id="sorting-results" style="display: none;">
            <div class="results-card">
              <div class="results-header">
                <h3><i class="fas fa-check-circle text-success"></i> Sorting Complete</h3>
                <p>Your PDF has been successfully sorted and is ready for download</p>
              </div>

              <div class="results-summary">
                <div class="summary-stats">
                  <div class="summary-item">
                    <div class="summary-icon">
                      <i class="fas fa-file-pdf"></i>
                    </div>
                    <div class="summary-content">
                      <span class="summary-label">Total Payslips</span>
                      <span class="summary-value" id="total-payslips-sorted">0</span>
                    </div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-icon">
                      <i class="fas fa-clock"></i>
                    </div>
                    <div class="summary-content">
                      <span class="summary-label">Processing Time</span>
                      <span class="summary-value" id="processing-time">0s</span>
                    </div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-icon">
                      <i class="fas fa-sort"></i>
                    </div>
                    <div class="summary-content">
                      <span class="summary-label">Sort Criteria</span>
                      <span class="summary-value" id="sort-criteria-used">Employee No.</span>
                    </div>
                  </div>
                </div>

                <div class="sorting-preview" id="sorting-preview">
                  <h4><i class="fas fa-eye"></i> Sorting Preview</h4>
                  <div class="preview-list" id="preview-list">
                    <!-- Preview items will be populated here -->
                  </div>
                </div>
              </div>

              <div class="results-actions">
                <button class="btn btn-primary btn-lg" id="download-sorted-pdf-btn">
                  <i class="fas fa-download"></i> Download Sorted PDF
                </button>
                <button class="btn btn-secondary" id="save-to-reports-btn">
                  <i class="fas fa-save"></i> Save to Reports
                </button>
                <button class="btn btn-outline-primary" id="sort-another-btn">
                  <i class="fas fa-plus"></i> Sort Another PDF
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Data Builder Tab -->
      <div id="data-builder-tab" class="tab-content">
        <div class="data-builder-container">
          <div class="data-builder-header">
            <h2><i class="fas fa-table"></i> Data Builder</h2>
            <p class="description">Build comprehensive spreadsheet data from payroll files for analysis and decision-making</p>
          </div>

          <!-- File Upload Section -->
          <div class="data-builder-section">
            <h3><i class="fas fa-upload"></i> Upload Payroll File</h3>
            <div class="upload-area" id="data-builder-upload-area">
              <div class="upload-content">
                <i class="fas fa-file-pdf upload-icon"></i>
                <p class="upload-text">Select payroll PDF file to build spreadsheet data</p>
                <input type="file" id="data-builder-file-input" accept=".pdf" style="display: none;">
                <button class="btn btn-primary" id="select-data-builder-file-btn">
                  <i class="fas fa-folder-open"></i> Select Payroll File
                </button>
              </div>
            </div>

            <!-- File Info Display -->
            <div id="data-builder-file-info" class="file-info" style="display: none;">
              <div class="file-details">
                <h4><i class="fas fa-file-pdf"></i> Selected File</h4>
                <p id="data-builder-file-name">No file selected</p>
                <p id="data-builder-file-size">Size: 0 MB</p>
                <button class="btn btn-secondary" id="change-data-builder-file-btn">
                  <i class="fas fa-exchange-alt"></i> Change File
                </button>
              </div>
            </div>
          </div>

          <!-- Dictionary Integration Section -->
          <div class="data-builder-section">
            <h3><i class="fas fa-book"></i> Dictionary & Column Configuration</h3>

            <!-- Dictionary Manager Interface -->
            <div class="dictionary-manager-container">
              <!-- Section Tabs -->
              <div class="section-tabs">
                <button class="section-tab active" data-section="PERSONAL DETAILS">
                  <i class="fas fa-user"></i> Personal Details
                </button>
                <button class="section-tab" data-section="EARNINGS">
                  <i class="fas fa-money-bill-wave"></i> Earnings
                </button>
                <button class="section-tab" data-section="DEDUCTIONS">
                  <i class="fas fa-minus-circle"></i> Deductions
                </button>
                <button class="section-tab" data-section="EMPLOYERS CONTRIBUTION">
                  <i class="fas fa-building"></i> Employer Contributions
                </button>
                <button class="section-tab" data-section="LOANS">
                  <i class="fas fa-credit-card"></i> Loans
                </button>
                <button class="section-tab" data-section="EMPLOYEE BANK DETAILS">
                  <i class="fas fa-university"></i> Bank Details
                </button>
              </div>

              <!-- Global Controls -->
              <div class="dictionary-controls">
                <div class="control-group">
                  <label>
                    <input type="checkbox" id="select-all-items" checked>
                    <span>Select All Items</span>
                  </label>
                </div>
                <div class="control-group">
                  <button class="btn btn-info" id="refresh-dictionary-btn">
                    <i class="fas fa-sync-alt"></i> Refresh Dictionary
                  </button>
                </div>
                <div class="control-group">
                  <button class="btn btn-success" id="add-item-btn">
                    <i class="fas fa-plus"></i> Add Item
                  </button>
                </div>
              </div>

              <!-- Section Content -->
              <div class="section-content">
                <!-- Personal Details Section -->
                <div class="section-panel active" data-section="PERSONAL DETAILS">
                  <div class="section-header">
                    <h4>Personal Details Items</h4>
                  </div>
                  <div class="dictionary-table-wrapper">
                    <table class="dictionary-table">
                      <thead>
                        <tr>
                          <th>ITEM NAME</th>
                          <th>FORMAT</th>
                          <th>VALUE FORMAT</th>
                          <th>INCLUDE IN REPORT</th>
                          <th>ACTIONS</th>
                        </tr>
                      </thead>
                      <tbody id="personal-details-table-body">
                        <!-- Items will be populated here -->
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Earnings Section -->
                <div class="section-panel" data-section="EARNINGS">
                  <div class="section-header">
                    <h4>Earnings Items</h4>
                  </div>
                  <div class="dictionary-table-wrapper">
                    <table class="dictionary-table">
                      <thead>
                        <tr>
                          <th>ITEM NAME</th>
                          <th>FORMAT</th>
                          <th>VALUE FORMAT</th>
                          <th>INCLUDE IN REPORT</th>
                          <th>ACTIONS</th>
                        </tr>
                      </thead>
                      <tbody id="earnings-table-body">
                        <!-- Items will be populated here -->
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Deductions Section -->
                <div class="section-panel" data-section="DEDUCTIONS">
                  <div class="section-header">
                    <h4>Deductions Items</h4>
                  </div>
                  <div class="dictionary-table-wrapper">
                    <table class="dictionary-table">
                      <thead>
                        <tr>
                          <th>ITEM NAME</th>
                          <th>FORMAT</th>
                          <th>VALUE FORMAT</th>
                          <th>INCLUDE IN REPORT</th>
                          <th>ACTIONS</th>
                        </tr>
                      </thead>
                      <tbody id="deductions-table-body">
                        <!-- Items will be populated here -->
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Employers Contribution Section -->
                <div class="section-panel" data-section="EMPLOYERS CONTRIBUTION">
                  <div class="section-header">
                    <h4>Employers Contribution Items</h4>
                  </div>
                  <div class="dictionary-table-wrapper">
                    <table class="dictionary-table">
                      <thead>
                        <tr>
                          <th>ITEM NAME</th>
                          <th>FORMAT</th>
                          <th>VALUE FORMAT</th>
                          <th>INCLUDE IN REPORT</th>
                          <th>ACTIONS</th>
                        </tr>
                      </thead>
                      <tbody id="employers-contribution-table-body">
                        <!-- Items will be populated here -->
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Loans Section -->
                <div class="section-panel" data-section="LOANS">
                  <div class="section-header">
                    <h4>Loans Items</h4>
                  </div>
                  <div class="dictionary-table-wrapper">
                    <table class="dictionary-table">
                      <thead>
                        <tr>
                          <th>ITEM NAME</th>
                          <th>FORMAT</th>
                          <th>VALUE FORMAT</th>
                          <th>INCLUDE IN REPORT</th>
                          <th>ACTIONS</th>
                        </tr>
                      </thead>
                      <tbody id="loans-table-body">
                        <!-- Items will be populated here -->
                      </tbody>
                    </table>
                  </div>
                </div>

                <!-- Employee Bank Details Section -->
                <div class="section-panel" data-section="EMPLOYEE BANK DETAILS">
                  <div class="section-header">
                    <h4>Employee Bank Details Items</h4>
                  </div>
                  <div class="dictionary-table-wrapper">
                    <table class="dictionary-table">
                      <thead>
                        <tr>
                          <th>ITEM NAME</th>
                          <th>FORMAT</th>
                          <th>VALUE FORMAT</th>
                          <th>INCLUDE IN REPORT</th>
                          <th>ACTIONS</th>
                        </tr>
                      </thead>
                      <tbody id="employee-bank-details-table-body">
                        <!-- Items will be populated here -->
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Processing Section -->
          <div class="data-builder-section">
            <h3><i class="fas fa-cogs"></i> Build Spreadsheet</h3>

            <div class="build-controls">
              <div class="control-group">
                <label for="month-year-input">Month/Year:</label>
                <input type="month" id="month-year-input" class="form-control">
              </div>

              <div class="control-group">
                <label>
                  <input type="checkbox" id="include-analytics" checked>
                  <span>Include Analytics Sheet</span>
                </label>
              </div>

              <div class="control-group">
                <label>
                  <input type="checkbox" id="include-varying-items" checked>
                  <span>Auto-capture Varying Items</span>
                </label>
              </div>
            </div>

            <div class="build-actions">
              <button class="btn btn-success btn-large" id="build-spreadsheet-btn">
                <i class="fas fa-play"></i> Build Spreadsheet
              </button>
              <button class="btn btn-warning" id="preview-columns-btn">
                <i class="fas fa-eye"></i> Preview Columns
              </button>
            </div>
          </div>

          <!-- Progress Section -->
          <div id="data-builder-progress-section" class="progress-section" style="display: none;">
            <h3><i class="fas fa-spinner fa-spin"></i> Building Spreadsheet...</h3>
            <div class="progress-bar">
              <div class="progress-fill" id="data-builder-progress-fill"></div>
            </div>
            <p id="data-builder-progress-text">Initializing...</p>
            <button class="btn btn-danger" id="cancel-data-builder-btn">
              <i class="fas fa-stop"></i> Cancel
            </button>
          </div>

          <!-- Results Section -->
          <div id="data-builder-results-section" class="results-section" style="display: none;">
            <h3><i class="fas fa-chart-bar"></i> Spreadsheet Generated</h3>

            <div class="results-summary">
              <div class="summary-card">
                <h4>Data Summary</h4>
                <p id="total-employees">Employees: 0</p>
                <p id="total-columns">Columns: 0</p>
                <p id="processing-time">Time: 0s</p>
              </div>

              <div class="summary-card">
                <h4>File Information</h4>
                <p id="output-file-name">File: Not generated</p>
                <p id="output-file-size">Size: 0 MB</p>
                <p id="output-location">Location: Not saved</p>
              </div>
            </div>

            <div class="results-actions">
              <button class="btn btn-primary" id="download-spreadsheet-btn">
                <i class="fas fa-download"></i> Download Spreadsheet
              </button>
              <button class="btn btn-info" id="view-analytics-btn">
                <i class="fas fa-chart-line"></i> View Analytics
              </button>
              <button class="btn btn-secondary" id="view-report-btn">
                <i class="fas fa-eye"></i> View Report
              </button>
            </div>
          </div>

          <!-- Analytics Section -->
          <div id="data-builder-analytics-section" class="analytics-section" style="display: none;">
            <h3><i class="fas fa-analytics"></i> Data Analytics & Insights</h3>

            <div class="analytics-tabs">
              <button class="analytics-tab active" data-tab="summary">Summary</button>
              <button class="analytics-tab" data-tab="earnings">Earnings Analysis</button>
              <button class="analytics-tab" data-tab="deductions">Deductions Analysis</button>
              <button class="analytics-tab" data-tab="departments">Department Breakdown</button>
            </div>

            <div class="analytics-content">
              <div id="analytics-summary" class="analytics-panel active">
                <!-- Summary analytics will be displayed here -->
              </div>
              <div id="analytics-earnings" class="analytics-panel">
                <!-- Earnings analytics will be displayed here -->
              </div>
              <div id="analytics-deductions" class="analytics-panel">
                <!-- Deductions analytics will be displayed here -->
              </div>
              <div id="analytics-departments" class="analytics-panel">
                <!-- Department analytics will be displayed here -->
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Bank Adviser Tab -->
      <div id="bank-adviser-tab" class="tab-content">
        <div class="bank-adviser-main-container">
          <div class="bank-adviser-header">
            <h2><i class="fas fa-university"></i> Bank Adviser Module</h2>
            <p class="tab-description">Generate Excel Bank Advice and track loans & allowances with comprehensive mini-database functionality</p>
          </div>

          <!-- Bank Adviser Sub-Tabs -->
          <div class="bank-adviser-tabs">
            <button class="bank-adviser-tab-btn active" data-subtab="core-adviser">
              <i class="fas fa-file-excel"></i> Core Bank Adviser
            </button>
            <button class="bank-adviser-tab-btn" data-subtab="loan-tracker">
              <i class="fas fa-chart-line"></i> Loan & Allowance Tracker
            </button>
          </div>

          <!-- Core Bank Adviser Content -->
          <div id="core-adviser-content" class="bank-adviser-subtab-content active">
            <div id="bank-adviser-content">
              <!-- Core Bank Adviser interface will be populated by bank_adviser.js -->
            </div>
          </div>

          <!-- Loan & Allowance Tracker Content -->
          <div id="loan-tracker-subtab" class="bank-adviser-subtab-content">
            <div id="loan-tracker-content">
              <!-- Loan & Allowance Tracker interface will be populated by bank_adviser.js -->
            </div>
          </div>
        </div>
      </div>
    </main>

    <!-- App Footer -->
    <footer class="app-footer">
      <div class="footer-content">
        <div class="footer-left">
          <div class="footer-app-name">
            <i class="fas fa-file-invoice-dollar"></i>
            <span>TEMPLAR PAYROLL AUDITOR</span>
          </div>
          <div class="footer-version">Version 2.0.0</div>
        </div>
        <div class="footer-center">
          <div class="footer-copyright">
            <strong>Created By:</strong> SAMUEL ASIEDU
          </div>
          <div class="footer-rights">
            2025 TEMPLAR PAYROLL AUDITOR. All Rights Reserved.
          </div>
        </div>
        <div class="footer-right">
          <div class="footer-tech">
            Professional Payroll Processing System
          </div>
          <div class="footer-contact">
            Powered by Advanced AI Extraction Technology
          </div>
        </div>
      </div>

      <!-- Status Bar -->
      <div class="status-bar">
        <div class="status-left">
          <span id="status-message">Ready</span>
        </div>
        <div class="status-right">
          <span id="processing-progress" style="display: none;">
            Processing... <span id="progress-percentage">0%</span>
          </span>
        </div>
      </div>
    </footer>
  </div>

  <!-- Report Viewer Container (Positioned absolutely) -->
  <div id="report-viewer-container" class="report-viewer-container" style="display: none;">
    <!-- Report content will be inserted here dynamically -->
  </div>

  <!-- Load JavaScript -->
  <!-- CRITICAL FIX: Define ContentSwitchingManager directly in HTML to avoid load order issues -->
  <script>
    // Define ContentSwitchingManager directly in HTML to guarantee it's available
    // before any other scripts try to use it
    console.log('🔄 Defining ContentSwitchingManager directly in HTML');
    window.ContentSwitchingManager = class ContentSwitchingManager {
      constructor() {
        this.currentPhase = null;
        this.phases = [
          'extraction',
          'pre-auditing',
          'comparison',
          'tracker-feeding',
          'tracker-learning',
          'auto-learning',
          'pre-reporting',
          'report-generation'
        ];
        this.initialized = false;
        console.log('✅ ContentSwitchingManager instance created');
      }

      initialize() {
        if (this.initialized) return;
        console.log('✅ ContentSwitchingManager initialized');
        this.initialized = true;
      }
    };
    console.log('✅ ContentSwitchingManager defined successfully:', typeof window.ContentSwitchingManager);
  </script>
  <script src="ui/business_rules_engine.js"></script>
  <script src="ui/smart_report_generator.js"></script>
  <script src="ui/word_template_engine.js"></script>
  <script src="ui/interactive_pre_reporting.js"></script>
  <script src="renderer.js"></script>
  <script src="ui/dictionary_manager.js"></script>
  <script src="ui/stats_panel_updater.js"></script>
  <script src="ui/data_builder.js"></script>
  <script src="ui/bank_adviser.js"></script>
  <!-- CRITICAL: Load content_switching_manager.js AFTER renderer.js to ensure functions are available -->
  <script src="ui/content_switching_manager.js"></script>

  <!-- Application Initialization Script -->
  <script>
    // Word Template Engine Test Function (Temporary)
    function testWordTemplateEngine() {
        console.log('Testing Word Template Engine...');
        console.log('Available window properties:', Object.keys(window).filter(key => key.includes('Word')));

        if (typeof WordTemplateEngine === 'undefined') {
            alert('❌ WordTemplateEngine is not loaded!\nCheck console for script loading errors.');
            console.error('WordTemplateEngine is not defined');
            console.log('Checking if script was loaded...');

            // Check if the script element exists
            const scriptElement = document.querySelector('script[src="ui/word_template_engine.js"]');
            console.log('Script element found:', !!scriptElement);
            if (scriptElement) {
                console.log('Script src:', scriptElement.src);
            }
            return;
        }

        try {
            // Test initialization
            console.log('Creating WordTemplateEngine instance...');
            const engine = new WordTemplateEngine();
            console.log('✅ WordTemplateEngine created successfully:', engine);

            // Test basic functionality
            console.log('Testing basic functionality...');
            const templateStructure = engine.templateStructure;
            console.log('✅ Template structure loaded:', templateStructure);

            alert('✅ Word Template Engine is working correctly!\nCheck console for details.');
        } catch (error) {
            console.error('❌ Error testing WordTemplateEngine:', error);
            alert('❌ Error testing Word Template Engine:\n' + error.message + '\n\nCheck console for full details.');
        }
    }

    // Application initialization and splash screen handling
    document.addEventListener('DOMContentLoaded', function() {
      console.log('🚀 THE PAYROLL AUDITOR - DOM Content Loaded');

      // Initialize application components
      initializeApplication();

      // Add test button event listener
      const testButton = document.getElementById('test-word-engine');
      if (testButton) {
          testButton.addEventListener('click', testWordTemplateEngine);
      }
    });

    function initializeApplication() {
      console.log('🔧 Initializing application components...');

      // FALLBACK STRATEGY: Initialize ContentSwitchingManager (using fallback version)
      if (window.ContentSwitchingManager && !window.contentSwitchingManager) {
        console.log('🔧 Initializing Fallback ContentSwitchingManager...');
        window.contentSwitchingManager = new window.ContentSwitchingManager();
        window.contentSwitchingManager.initialize();
        console.log('✅ Fallback ContentSwitchingManager initialized successfully');
        console.log('📋 Using reliable fallback version with core functionality');
      }

      // Set up database initialization listener
      if (window.api && window.api.onDatabaseInitialized) {
        window.api.onDatabaseInitialized((data) => {
          console.log('✅ Database initialized:', data);
          hideSplashScreen();
        });
      }

      // Set up tab navigation
      setupTabNavigation();

      // Initialize default tab content
      initializeDefaultTab();

      // Fallback: Hide splash screen after maximum wait time
      setTimeout(() => {
        console.log('⏰ Fallback: Hiding splash screen after timeout');
        hideSplashScreen();
      }, 5000); // 5 second fallback
    }

    function hideSplashScreen() {
      console.log('🎭 Hiding splash screen and showing main application');

      const splashScreen = document.getElementById('splash-screen');
      const mainApp = document.getElementById('main-app');

      if (splashScreen && mainApp) {
        // Fade out splash screen
        splashScreen.style.opacity = '0';

        setTimeout(() => {
          splashScreen.style.display = 'none';
          mainApp.style.display = 'block';

          // Trigger any post-initialization tasks
          onApplicationReady();
        }, 300); // Match the CSS transition duration
      } else {
        console.error('❌ Splash screen or main app elements not found');
      }
    }

    function setupTabNavigation() {
      console.log('📋 Setting up tab navigation');

      const navTabs = document.querySelectorAll('.nav-tab');
      const tabContents = document.querySelectorAll('.tab-content');

      navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
          const targetTab = this.getAttribute('data-tab');

          // Remove active class from all tabs and contents
          navTabs.forEach(t => t.classList.remove('active'));
          tabContents.forEach(content => content.classList.remove('active'));

          // Add active class to clicked tab and corresponding content
          this.classList.add('active');
          const targetContent = document.getElementById(targetTab + '-tab');
          if (targetContent) {
            targetContent.classList.add('active');
          }

          console.log('📂 Switched to tab:', targetTab);

          // Initialize tab-specific functionality
          initializeTabContent(targetTab);
        });
      });
    }

    function initializeTabContent(tabName) {
      console.log('🔧 Initializing content for tab:', tabName);

      // Call the appropriate initialization function based on the tab
      switch(tabName) {
        case 'pdf-sorter':
          if (typeof initializePdfSorterTab === 'function') {
            initializePdfSorterTab();
          } else {
            console.warn('⚠️ initializePdfSorterTab function not available');
          }
          break;

        case 'report-manager':
          if (typeof initializeReportManagerTab === 'function') {
            initializeReportManagerTab();
          } else {
            console.warn('⚠️ initializeReportManagerTab function not available');
          }
          break;

        case 'data-builder':
          if (typeof initializeDataBuilderTab === 'function') {
            initializeDataBuilderTab();
          } else {
            console.warn('⚠️ initializeDataBuilderTab function not available');
          }
          // Initialize the Data Builder module
          if (typeof initializeDataBuilderModule === 'function') {
            initializeDataBuilderModule();
          } else if (window.dataBuilder && typeof window.dataBuilder.initializeModule === 'function') {
            window.dataBuilder.initializeModule();
          } else {
            console.warn('⚠️ Data Builder module not available');
          }
          break;

        case 'bank-adviser':
          if (typeof initializeBankAdviserTab === 'function') {
            initializeBankAdviserTab();
          } else {
            console.warn('⚠️ initializeBankAdviserTab function not available');
          }
          break;

        case 'dictionary':
          if (typeof initDictionaryManager === 'function') {
            initDictionaryManager();
          } else {
            console.warn('⚠️ initDictionaryManager function not available');
          }
          break;

        case 'payroll-audit':
          // CRITICAL: Ensure startPayrollAuditProcess is globally accessible before content switching
          if (typeof startPayrollAuditProcess === 'function' && !window.startPayrollAuditProcess) {
            window.startPayrollAuditProcess = startPayrollAuditProcess;
            console.log('✅ startPayrollAuditProcess made globally accessible in tab initialization');
          }

          // Payroll audit tab initialization is handled by content switching manager
          if (typeof initializeContentSwitching === 'function') {
            initializeContentSwitching();
          }
          // CRITICAL: Check audit button state when payroll audit tab is accessed
          setTimeout(() => {
            if (typeof checkAuditButtonState === 'function') {
              checkAuditButtonState();
              console.log('🔍 Audit button state checked for payroll audit tab');
            }
          }, 200);
          break;

        case 'home':
          // Home tab doesn't need special initialization
          console.log('🏠 Home tab activated');
          break;

        default:
          console.log('📋 No specific initialization needed for tab:', tabName);
      }
    }

    function initializeDefaultTab() {
      console.log('🏠 Initializing default tab (Payroll Audit)');

      // Ensure the payroll audit tab is active by default
      const payrollAuditTab = document.querySelector('[data-tab="payroll-audit"]');
      const payrollAuditContent = document.getElementById('payroll-audit-tab');

      if (payrollAuditTab && payrollAuditContent) {
        payrollAuditTab.classList.add('active');
        payrollAuditContent.classList.add('active');
      }
    }

    function onApplicationReady() {
      console.log('🎉 Application ready and fully initialized');

      // Update status
      const statusMessage = document.getElementById('status-message');
      if (statusMessage) {
        statusMessage.textContent = 'Application Ready';
      }

      // CRITICAL: Ensure startPayrollAuditProcess is globally accessible before content switching
      if (typeof startPayrollAuditProcess === 'function' && !window.startPayrollAuditProcess) {
        window.startPayrollAuditProcess = startPayrollAuditProcess;
        console.log('✅ startPayrollAuditProcess made globally accessible in onApplicationReady');
      }

      // Initialize any additional components that need the full UI to be ready
      if (typeof initializeContentSwitching === 'function') {
        initializeContentSwitching();
      }

      // Set up all UI event listeners with retry mechanism
      const setupEventListeners = () => {
        let success = false;

        if (typeof setupUIEventListeners === 'function') {
          setupUIEventListeners();
          console.log('🔧 UI Event listeners initialized');
          success = true;
        }

        // CRITICAL FIX: Also set up real-time event listeners for progress updates
        if (typeof setupRealTimeEventListeners === 'function') {
          setupRealTimeEventListeners();
          console.log('🔄 Real-time event listeners initialized');
          success = true;
        }

        return success;
      };

      // Try immediately, then retry if needed
      if (!setupEventListeners()) {
        console.log('⏳ Event listeners not ready yet, retrying...');
        setTimeout(() => {
          if (!setupEventListeners()) {
            setTimeout(() => {
              if (!setupEventListeners()) {
                console.error('❌ Event listener functions not available after retries');
              }
            }, 1000);
          }
        }, 500);
      }

      // Initialize the default tab content (Payroll Audit)
      setTimeout(() => {
        initializeTabContent('payroll-audit');
        console.log('🏠 Default tab content initialized');
      }, 100);

      // Load system status
      loadSystemStatus();
    }

    async function loadSystemStatus() {
      try {
        if (window.api && window.api.getSystemStatus) {
          const status = await window.api.getSystemStatus();
          console.log('📊 System status loaded:', status);

          // Update UI with system status
          updateSystemStatusDisplay(status);
        }
      } catch (error) {
        console.error('❌ Error loading system status:', error);
      }
    }

    function updateSystemStatusDisplay(status) {
      // Update backend status
      const backendStatus = document.getElementById('backend-status');
      if (backendStatus && status.success) {
        backendStatus.textContent = 'Ready';
        backendStatus.className = 'status-value status-ready';
      }

      // Update extraction engine status
      const extractionEngine = document.getElementById('extraction-engine');
      if (extractionEngine) {
        extractionEngine.textContent = 'Perfect + Hybrid';
      }

      // Load dictionary count
      loadDictionaryCount();
    }

    async function loadDictionaryCount() {
      try {
        if (window.api && window.api.getDictionaryStats) {
          const stats = await window.api.getDictionaryStats();
          const dictionaryCount = document.getElementById('dictionary-count');
          if (dictionaryCount && stats) {
            dictionaryCount.textContent = stats.total_items || '0';
          }
        }
      } catch (error) {
        console.error('❌ Error loading dictionary count:', error);
        const dictionaryCount = document.getElementById('dictionary-count');
        if (dictionaryCount) {
          dictionaryCount.textContent = 'Error';
        }
      }
    }
  </script>
</body>
</html>
