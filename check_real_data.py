#!/usr/bin/env python3
"""
Quick check of real data available for report generation testing
"""

import sqlite3
import os
import json

def check_real_data():
    """Check what real data is available"""
    print("🔍 CHECKING REAL DATA FOR REPORT GENERATION")
    print("=" * 50)
    
    # Check if we're in the right directory
    current_files = os.listdir('.')
    if 'data' not in current_files:
        print("❌ Not in the right directory - no 'data' folder found")
        print(f"Current files: {current_files[:10]}")
        return False
    
    db_path = './data/templar_payroll_auditor.db'
    if not os.path.exists(db_path):
        print(f"❌ Database not found: {db_path}")
        return False
    
    print(f"✅ Database found: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check current session
        cursor.execute('SELECT session_id FROM current_session WHERE id = 1')
        current_session = cursor.fetchone()
        
        if current_session:
            session_id = current_session[0]
            print(f"✅ Current Session: {session_id}")
            
            # Check comparison results
            cursor.execute('SELECT COUNT(*) FROM comparison_results WHERE session_id = ?', (session_id,))
            comparison_count = cursor.fetchone()[0]
            print(f"📊 Comparison Results: {comparison_count} changes")
            
            if comparison_count > 0:
                # Get sample data
                cursor.execute('''
                    SELECT employee_id, employee_name, section, change_type, priority 
                    FROM comparison_results 
                    WHERE session_id = ? 
                    LIMIT 5
                ''', (session_id,))
                samples = cursor.fetchall()
                
                print("\n🔍 Sample Changes:")
                for i, sample in enumerate(samples, 1):
                    print(f"  {i}. {sample[1]} ({sample[0]}) - {sample[2]}: {sample[3]} [{sample[4]}]")
                
                # Priority breakdown
                cursor.execute('''
                    SELECT priority, COUNT(*) 
                    FROM comparison_results 
                    WHERE session_id = ? 
                    GROUP BY priority
                ''', (session_id,))
                priorities = cursor.fetchall()
                
                print("\n📊 Priority Breakdown:")
                for priority, count in priorities:
                    print(f"  {priority}: {count} changes")
                
                print(f"\n✅ READY FOR TESTING!")
                print(f"Session ID: {session_id}")
                print(f"Total Changes: {comparison_count}")
                print("\n🎯 You can now test report generation in the UI:")
                print("1. Go to the Final Report Interface")
                print("2. Configure report settings (Generated By, Designation)")
                print("3. Select changes to include")
                print("4. Click 'Generate FINAL-REPORT' to test")
                
                return True
            else:
                print("❌ No comparison results found")
                return False
        else:
            print("❌ No current session found")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    finally:
        conn.close()

if __name__ == "__main__":
    check_real_data()
